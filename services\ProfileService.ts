import { supabase, getCurrentUserId } from "../config/supabase";
import { UserProfile } from "../context/UserProfileContext";

interface SupabaseProfile {
  id: string;
  weight: number;
  height: number;
  age: number;
  gender: string;
  activity_level: string;
  goal_type: string;
  target_weight?: number;
  weight_change_rate?: number;
  daily_calorie_goal: number;
  protein: number;
  carbs: number;
  fat: number;
  initialized: boolean;
}

// Convert from app format to database format
const transformProfileForSupabase = async (
  profile: UserProfile
): Promise<SupabaseProfile> => {
  const userId = await getCurrentUserId();
  if (!userId) {
    throw new Error(
      "User ID not available. Please ensure you are authenticated."
    );
  }
  return {
    id: userId,
    weight: profile.weight,
    height: profile.height,
    age: profile.age,
    gender: profile.gender,
    activity_level: profile.activityLevel,
    goal_type: profile.goalType,
    target_weight: profile.targetWeight,
    weight_change_rate: profile.weightChangeRate,
    daily_calorie_goal: profile.dailyCalorieGoal,
    protein: profile.macroGoals.protein,
    carbs: profile.macroGoals.carbs,
    fat: profile.macroGoals.fat,
    initialized: profile.initialized,
  };
};

// Convert from database format to app format
const transformProfileFromSupabase = (
  profile: SupabaseProfile
): UserProfile => {
  return {
    weight: profile.weight,
    height: profile.height,
    age: profile.age,
    gender: profile.gender as "male" | "female" | "other",
    activityLevel: profile.activity_level as any,
    goalType: profile.goal_type as any,
    targetWeight: profile.target_weight,
    weightChangeRate: profile.weight_change_rate,
    dailyCalorieGoal: profile.daily_calorie_goal,
    macroGoals: {
      protein: profile.protein,
      carbs: profile.carbs,
      fat: profile.fat,
    },
    initialized: profile.initialized,
  };
};

// Get user profile from Supabase
export const getProfile = async (): Promise<UserProfile | null> => {
  try {
    const userId = await getCurrentUserId();

    if (!userId) {
      console.error(
        "User ID not available. Please ensure you are authenticated."
      );
      return null;
    }

    const { data, error } = await supabase
      .from("profiles")
      .select("*")
      .eq("id", userId)
      .maybeSingle();

    if (error) {
      console.error("Error fetching profile:", error);
      return null;
    }

    if (!data) {
      return null;
    }

    return transformProfileFromSupabase(data);
  } catch (error) {
    console.error("Error in getProfile:", error);
    return null;
  }
};

// Create or update user profile in Supabase
export const saveProfile = async (profile: UserProfile): Promise<boolean> => {
  try {
    const supabaseProfile = await transformProfileForSupabase(profile);
    const userId = await getCurrentUserId();

    if (!userId) {
      console.error(
        "User ID not available. Please ensure you are authenticated."
      );
      return false;
    }

    // Check if profile exists
    const { data: existingProfile, error: queryError } = await supabase
      .from("profiles")
      .select("id")
      .eq("id", userId)
      .maybeSingle();

    if (queryError) {
      console.error("Error checking if profile exists:", queryError);
      return false;
    }

    if (existingProfile) {
      // Update existing profile
      const { error } = await supabase
        .from("profiles")
        .update(supabaseProfile)
        .eq("id", userId);

      if (error) {
        console.error("Error updating profile:", error);
        return false;
      }
    } else {
      // Insert new profile
      const { error } = await supabase.from("profiles").insert(supabaseProfile);

      if (error) {
        console.error("Error creating profile:", error);
        return false;
      }
    }

    return true;
  } catch (error) {
    console.error("Error in saveProfile:", error);
    return false;
  }
};
