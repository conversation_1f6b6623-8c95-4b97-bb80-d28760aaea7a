import { supabase, getCurrentUserId } from "../config/supabase";
import { FoodItem } from "../context/FoodLogContext";

interface SupabaseFoodItem {
  id: string;
  user_id: string;
  name: string;
  calories: number;
  protein: number;
  carbs: number;
  fat: number;
  serving_size: string;
  image_url?: string;
  date: string; // Now a timestamptz in the database
  meal_type: string;
  created_at: string;
}

// Function to format date for timestamptz queries
const formatDateToTimestampRange = (dateString: string) => {
  // Convert date string (YYYY-MM-DD) to timestamp range for that day
  const startDate = new Date(`${dateString}T00:00:00.000Z`);
  const endDate = new Date(`${dateString}T23:59:59.999Z`);
  return { startDate, endDate };
};

// Convert from app format to database format
const transformFoodItemForSupabase = async (
  item: Omit<FoodItem, "id" | "createdAt">
): Promise<Omit<SupabaseFoodItem, "id" | "created_at">> => {
  const userId = await getCurrentUserId();

  if (!userId) {
    throw new Error(
      "User ID not available. Please ensure you are authenticated."
    );
  }

  // Format date string to ISO format with time
  const dateWithTime = new Date(`${item.date}T12:00:00.000Z`).toISOString();

  return {
    user_id: userId,
    name: item.name,
    calories: item.calories,
    protein: item.protein,
    carbs: item.carbs,
    fat: item.fat,
    serving_size: item.servingSize,
    image_url: item.imageUri || item.image,
    date: dateWithTime, // Use the formatted timestamp
    meal_type: item.mealType,
  };
};

// Convert from database format to app format
const transformFoodItemFromSupabase = (item: SupabaseFoodItem): FoodItem => {
  // Extract only the date portion (YYYY-MM-DD) from the timestamp
  const dateOnly = new Date(item.date).toISOString().split("T")[0];

  return {
    id: item.id,
    name: item.name,
    calories: item.calories,
    protein: item.protein,
    carbs: item.carbs,
    fat: item.fat,
    servingSize: item.serving_size,
    imageUri: item.image_url,
    date: dateOnly, // Store only the date part
    mealType: item.meal_type as any,
    createdAt: new Date(item.created_at).getTime(),
  };
};

// Get food logs for a specific date
export const getFoodLogsByDate = async (date: string): Promise<FoodItem[]> => {
  try {
    const userId = await getCurrentUserId();

    if (!userId) {
      console.error(
        "User ID not available. Please ensure you are authenticated."
      );
      return [];
    }

    // Get timestamp range for the specific date
    const { startDate, endDate } = formatDateToTimestampRange(date);

    const { data, error } = await supabase
      .from("food_logs")
      .select("*")
      .eq("user_id", userId)
      .gte("date", startDate.toISOString())
      .lte("date", endDate.toISOString())
      .order("date", { ascending: true });

    if (error) {
      console.error("Error fetching food logs:", error);
      return [];
    }

    return data?.map(transformFoodItemFromSupabase) || [];
  } catch (error) {
    console.error("Error in getFoodLogsByDate:", error);
    return [];
  }
};

// Get all food logs
export const getAllFoodLogs = async (): Promise<Record<string, FoodItem[]>> => {
  try {
    const userId = await getCurrentUserId();

    if (!userId) {
      console.error(
        "User ID not available. Please ensure you are authenticated."
      );
      return {};
    }

    const { data, error } = await supabase
      .from("food_logs")
      .select("*")
      .eq("user_id", userId)
      .order("date", { ascending: true });

    if (error) {
      console.error("Error fetching all food logs:", error);
      return {};
    }

    const foodLogs = data?.map(transformFoodItemFromSupabase) || [];

    // Group by date
    return foodLogs.reduce((acc, item) => {
      if (!acc[item.date]) {
        acc[item.date] = [];
      }
      acc[item.date].push(item);
      return acc;
    }, {} as Record<string, FoodItem[]>);
  } catch (error) {
    console.error("Error in getAllFoodLogs:", error);
    return {};
  }
};

// Add a new food item
export const addFoodItem = async (
  item: Omit<FoodItem, "id" | "createdAt">
): Promise<FoodItem | null> => {
  try {
    const supabaseItem = await transformFoodItemForSupabase(item);

    const { data, error } = await supabase
      .from("food_logs")
      .insert(supabaseItem)
      .select()
      .maybeSingle();

    if (error) {
      console.error("Error adding food item:", error);
      return null;
    }

    if (!data) {
      console.error("No data returned after inserting food item");
      return null;
    }

    return transformFoodItemFromSupabase(data);
  } catch (error) {
    console.error("Error in addFoodItem:", error);
    return null;
  }
};

// Delete a food item
export const deleteFoodItem = async (id: string): Promise<boolean> => {
  try {
    const userId = await getCurrentUserId();

    if (!userId) {
      console.error(
        "User ID not available. Please ensure you are authenticated."
      );
      return false;
    }

    const { error } = await supabase
      .from("food_logs")
      .delete()
      .eq("id", id)
      .eq("user_id", userId);

    if (error) {
      console.error("Error deleting food item:", error);
      return false;
    }

    return true;
  } catch (error) {
    console.error("Error in deleteFoodItem:", error);
    return false;
  }
};

// Clear all food logs for a specific date
export const clearFoodLogsForDay = async (date: string): Promise<boolean> => {
  try {
    const userId = await getCurrentUserId();

    if (!userId) {
      console.error(
        "User ID not available. Please ensure you are authenticated."
      );
      return false;
    }

    // Get timestamp range for the specific date
    const { startDate, endDate } = formatDateToTimestampRange(date);

    const { error } = await supabase
      .from("food_logs")
      .delete()
      .eq("user_id", userId)
      .gte("date", startDate.toISOString())
      .lte("date", endDate.toISOString());

    if (error) {
      console.error("Error clearing food logs for day:", error);
      return false;
    }

    return true;
  } catch (error) {
    console.error("Error in clearFoodLogsForDay:", error);
    return false;
  }
};
