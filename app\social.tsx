import React, { useState, useEffect } from "react";
import {
  View,
  Text,
  ScrollView,
  TouchableOpacity,
  Image,
  Alert,
  Animated,
  Share,
} from "react-native";
import { useRouter, useNavigation } from "expo-router";
import { Ionicons } from "@expo/vector-icons";
import { LinearGradient } from "expo-linear-gradient";
import * as Haptics from "expo-haptics";
import { BlurView } from "expo-blur";

import { useUserProfile } from "@/context/UserProfileContext";
import { useWeight, WeightEntry } from "@/context/WeightContext";
import { useFoodLog } from "@/context/FoodLogContext";
import { useExercise } from "@/context/ExerciseContext";

// Extended types for the components that don't have proper TypeScript interfaces
interface ExtendedUserProfile {
  weightUnit?: string;
}

export default function SocialScreen() {
  const router = useRouter();
  const navigation = useNavigation();
  const { profile } = useUserProfile();
  const extendedProfile: ExtendedUserProfile = { ...profile, weightUnit: "kg" };
  const { weightEntries } = useWeight();
  const weightHistory = weightEntries || [];

  const { getLogsByDate, getDailyNutrition } = useFoodLog();
  const todaysFoodItems = getLogsByDate(new Date().toISOString().split("T")[0]);
  const todaysCalories = getDailyNutrition(
    new Date().toISOString().split("T")[0]
  ).calories;
  const { calculateDailyCaloriesBurned } = useExercise();

  const [activeTab, setActiveTab] = useState<"feed" | "friends" | "challenges">(
    "feed"
  );
  const [showConnectSheet, setShowConnectSheet] = useState(false);

  // Animation refs
  const fadeAnim = React.useRef(new Animated.Value(0)).current;
  const slideAnim = React.useRef(new Animated.Value(20)).current;

  // Animate content when component mounts
  useEffect(() => {
    Animated.parallel([
      Animated.spring(fadeAnim, {
        toValue: 1,
        tension: 50,
        friction: 7,
        useNativeDriver: true,
      }),
      Animated.spring(slideAnim, {
        toValue: 0,
        tension: 50,
        friction: 7,
        useNativeDriver: true,
      }),
    ]).start();

    // Set navigation title
    navigation.setOptions({
      title: "Social",
    });
  }, []);

  // Mock data for the social feed
  const mockFriends = [
    {
      id: "1",
      name: "Sarah Johnson",
      avatar: "https://randomuser.me/api/portraits/women/44.jpg",
      streakDays: 15,
      lastActive: "Today",
      stats: {
        workouts: 3,
        weightLoss: 0.8,
        calorieDeficit: 250,
      },
    },
    {
      id: "2",
      name: "Mike Chen",
      avatar: "https://randomuser.me/api/portraits/men/32.jpg",
      streakDays: 7,
      lastActive: "Yesterday",
      stats: {
        workouts: 2,
        weightLoss: 0.5,
        calorieDeficit: 150,
      },
    },
    {
      id: "3",
      name: "Alex Taylor",
      avatar: "https://randomuser.me/api/portraits/women/67.jpg",
      streakDays: 22,
      lastActive: "Today",
      stats: {
        workouts: 4,
        weightLoss: 1.2,
        calorieDeficit: 320,
      },
    },
  ];

  // Mock data for challenges - with valid Ionicons names
  const mockChallenges = [
    {
      id: "1",
      title: "7-Day Water Challenge",
      description: "Drink 8 glasses of water every day for a week",
      participants: 128,
      progress: 5,
      total: 7,
      icon: "fitness" as any,
    },
    {
      id: "2",
      title: "No Sugar Weekend",
      description: "Avoid added sugar foods for the weekend",
      participants: 76,
      progress: 1,
      total: 2,
      icon: "nutrition" as any,
    },
    {
      id: "3",
      title: "10K Steps Daily",
      description: "Walk at least 10,000 steps every day for 2 weeks",
      participants: 213,
      progress: 8,
      total: 14,
      icon: "walk" as any,
    },
  ];

  const mockFeedPosts = [
    {
      id: "1",
      user: mockFriends[0],
      type: "achievement",
      content: "Completed a 5K run in 28 minutes!",
      likes: 12,
      comments: 3,
      timeAgo: "2h",
    },
    {
      id: "2",
      user: mockFriends[2],
      type: "weight_goal",
      content: "Reached my monthly weight goal! Down 5 pounds this month.",
      likes: 25,
      comments: 8,
      timeAgo: "5h",
    },
    {
      id: "3",
      user: mockFriends[1],
      type: "food_log",
      content: "Made a healthy protein smoothie bowl for breakfast!",
      foodImage:
        "https://images.unsplash.com/photo-1610970881699-44a5587cabec?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxzZWFyY2h8M3x8c21vb3RoaWUlMjBib3dsfGVufDB8fDB8fHww&auto=format&fit=crop&w=800&q=60",
      likes: 8,
      comments: 2,
      timeAgo: "1d",
    },
  ];

  const shareProgress = async () => {
    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);

    try {
      // Get today's date in a nice format
      const today = new Date().toLocaleDateString("en-US", {
        weekday: "long",
        month: "long",
        day: "numeric",
      });

      // Calculate weight loss from initial weight if available
      let weightLossText = "";
      if (weightHistory.length > 1) {
        const initialWeight = weightHistory[weightHistory.length - 1].weight;
        const currentWeight = weightHistory[0].weight;
        const weightDiff = initialWeight - currentWeight;

        if (weightDiff > 0) {
          weightLossText = `\n🎯 ${weightDiff.toFixed(1)} ${
            extendedProfile.weightUnit || "kg"
          } lost so far`;
        }
      }

      // Build the message
      const message =
        `My Fitness Update for ${today}:\n\n` +
        `🍽️ ${todaysCalories} calories consumed\n` +
        `🔥 ${calculateDailyCaloriesBurned(
          new Date().toISOString().split("T")[0]
        )} calories burned${weightLossText}\n\n` +
        `Tracking with Calorie AI!`;

      await Share.share({
        message,
      });
    } catch (error) {
      console.error("Error sharing progress:", error);
      Alert.alert("Error", "Failed to share progress");
    }
  };

  const renderFeedTab = () => (
    <View className="space-y-4">
      {/* Share your progress card */}
      <View className="bg-white/5 rounded-3xl p-6 shadow-lg">
        <Text className="text-xl font-semibold text-white mb-3">
          Share Your Progress
        </Text>
        <Text className="text-gray-400 mb-4">
          Let friends know about your fitness journey
        </Text>

        <View className="bg-white/10 rounded-xl p-4 mb-4">
          <View className="flex-row justify-between mb-3">
            <View className="flex-row items-center">
              <Ionicons name="nutrition" size={20} color="#60a5fa" />
              <Text className="text-white ml-2">Today's Calories</Text>
            </View>
            <Text className="text-white font-bold">{todaysCalories}</Text>
          </View>

          <View className="flex-row justify-between">
            <View className="flex-row items-center">
              <Ionicons name="flame" size={20} color="#f87171" />
              <Text className="text-white ml-2">Calories Burned</Text>
            </View>
            <Text className="text-white font-bold">
              {calculateDailyCaloriesBurned(
                new Date().toISOString().split("T")[0]
              )}
            </Text>
          </View>
        </View>

        <TouchableOpacity
          onPress={shareProgress}
          className="bg-gradient-to-r from-blue-500 to-blue-600 px-6 py-3 rounded-full flex-row justify-center items-center"
        >
          <Ionicons name="share-social" size={18} color="white" />
          <Text className="text-white font-semibold ml-2">Share Progress</Text>
        </TouchableOpacity>
      </View>

      {/* Community Feed */}
      <Text className="text-xl font-semibold text-white mb-2">
        Community Feed
      </Text>

      {mockFeedPosts.map((post) => (
        <View key={post.id} className="bg-white/5 rounded-3xl p-5 shadow-lg">
          <View className="flex-row items-center mb-3">
            <Image
              source={{ uri: post.user.avatar }}
              className="w-10 h-10 rounded-full"
            />
            <View className="ml-3">
              <Text className="text-white font-semibold">{post.user.name}</Text>
              <Text className="text-gray-400 text-xs">{post.timeAgo} ago</Text>
            </View>
          </View>

          <Text className="text-white mb-3">{post.content}</Text>

          {post.type === "food_log" && post.foodImage && (
            <Image
              source={{ uri: post.foodImage }}
              className="w-full h-48 rounded-xl mb-3"
              resizeMode="cover"
            />
          )}

          <View className="flex-row justify-between mt-2 pt-2 border-t border-white/10">
            <TouchableOpacity className="flex-row items-center">
              <Ionicons name="heart-outline" size={22} color="#d1d5db" />
              <Text className="text-gray-400 ml-1">{post.likes}</Text>
            </TouchableOpacity>

            <TouchableOpacity className="flex-row items-center">
              <Ionicons name="chatbubble-outline" size={20} color="#d1d5db" />
              <Text className="text-gray-400 ml-1">{post.comments}</Text>
            </TouchableOpacity>

            <TouchableOpacity>
              <Ionicons name="share-social-outline" size={22} color="#d1d5db" />
            </TouchableOpacity>
          </View>
        </View>
      ))}

      <TouchableOpacity
        onPress={() =>
          Alert.alert(
            "Coming Soon",
            "This feature will be available in a future update."
          )
        }
        className="bg-white/10 p-4 rounded-xl flex-row justify-center items-center my-2"
      >
        <Text className="text-white">View More Posts</Text>
      </TouchableOpacity>
    </View>
  );

  const renderFriendsTab = () => (
    <View className="space-y-4">
      <TouchableOpacity
        onPress={() => setShowConnectSheet(true)}
        className="bg-gradient-to-r from-blue-500 to-blue-600 p-4 rounded-xl flex-row justify-center items-center mb-4"
      >
        <Ionicons name="person-add" size={18} color="white" />
        <Text className="text-white font-semibold ml-2">Find Friends</Text>
      </TouchableOpacity>

      <Text className="text-xl font-semibold text-white mb-2">
        Your Friends
      </Text>

      {mockFriends.map((friend) => (
        <View key={friend.id} className="bg-white/5 rounded-2xl p-4 flex-row">
          <Image
            source={{ uri: friend.avatar }}
            className="w-14 h-14 rounded-full"
          />

          <View className="flex-1 ml-3 justify-center">
            <View className="flex-row justify-between">
              <Text className="text-white font-semibold">{friend.name}</Text>
              <View className="flex-row items-center">
                <Ionicons name="flame" size={14} color="#f87171" />
                <Text className="text-white text-xs ml-1">
                  {friend.streakDays} days
                </Text>
              </View>
            </View>

            <Text className="text-gray-400 text-xs">
              Active: {friend.lastActive}
            </Text>

            <View className="flex-row mt-2">
              <View className="bg-white/10 px-2 py-1 rounded-full mr-2">
                <Text className="text-white text-xs">
                  {friend.stats.workouts} workouts
                </Text>
              </View>
              <View className="bg-white/10 px-2 py-1 rounded-full">
                <Text className="text-white text-xs">
                  -{friend.stats.weightLoss} kg
                </Text>
              </View>
            </View>
          </View>

          <TouchableOpacity className="justify-center">
            <Ionicons name="chatbubble-outline" size={24} color="#d1d5db" />
          </TouchableOpacity>
        </View>
      ))}

      <TouchableOpacity
        onPress={() =>
          Alert.alert(
            "Coming Soon",
            "This feature will be available in a future update."
          )
        }
        className="bg-white/10 p-4 rounded-xl flex-row justify-center items-center my-2"
      >
        <Text className="text-white">See All Friends</Text>
      </TouchableOpacity>
    </View>
  );

  const renderChallengesTab = () => (
    <View className="space-y-4">
      <Text className="text-xl font-semibold text-white mb-2">
        Active Challenges
      </Text>

      {mockChallenges.map((challenge) => (
        <View
          key={challenge.id}
          className="bg-white/5 rounded-3xl p-5 shadow-lg"
        >
          <View className="flex-row items-center mb-3">
            <View className="w-10 h-10 bg-purple-500/20 rounded-full items-center justify-center">
              <Ionicons name={challenge.icon} size={20} color="#d8b4fe" />
            </View>
            <View className="ml-3 flex-1">
              <Text className="text-white font-semibold">
                {challenge.title}
              </Text>
              <Text className="text-gray-400 text-xs">
                {challenge.participants} participants
              </Text>
            </View>
            <TouchableOpacity
              onPress={() =>
                Alert.alert(
                  "Join Challenge",
                  `Would you like to join the ${challenge.title}?`,
                  [
                    { text: "Cancel", style: "cancel" },
                    { text: "Join", style: "default" },
                  ]
                )
              }
              className="bg-white/10 px-4 py-2 rounded-full"
            >
              <Text className="text-white">Join</Text>
            </TouchableOpacity>
          </View>

          <Text className="text-gray-300 mb-3">{challenge.description}</Text>

          <View className="bg-white/10 rounded-full h-3 mb-2">
            <View
              className="bg-gradient-to-r from-green-500 to-green-400 rounded-full h-3"
              style={{
                width: `${(challenge.progress / challenge.total) * 100}%`,
              }}
            />
          </View>

          <Text className="text-gray-400 text-xs">
            Progress: {challenge.progress}/{challenge.total} days completed
          </Text>
        </View>
      ))}

      <TouchableOpacity
        onPress={() =>
          Alert.alert(
            "Create Challenge",
            "Would you like to create a new challenge?",
            [
              { text: "Cancel", style: "cancel" },
              { text: "Create", style: "default" },
            ]
          )
        }
        className="bg-gradient-to-r from-purple-500 to-purple-600 p-4 rounded-xl flex-row justify-center items-center my-2"
      >
        <Ionicons name="add-circle" size={18} color="white" />
        <Text className="text-white font-semibold ml-2">Create Challenge</Text>
      </TouchableOpacity>
    </View>
  );

  return (
    <View className="flex-1 bg-[#0A0A0A]">
      <LinearGradient colors={["#1A1A1A", "#0A0A0A"]} className="flex-1">
        <ScrollView className="flex-1 p-5">
          {/* Header */}
          <View className="flex-row justify-between items-center mb-6">
            <TouchableOpacity
              onPress={() => router.back()}
              className="bg-white/10 p-2 rounded-full"
            >
              <Ionicons name="arrow-back" size={24} color="white" />
            </TouchableOpacity>
            <Text className="text-2xl font-bold text-white">Social</Text>
            <TouchableOpacity
              onPress={() =>
                Alert.alert("Notifications", "No new notifications")
              }
              className="bg-white/10 p-2 rounded-full"
            >
              <Ionicons name="notifications" size={24} color="white" />
            </TouchableOpacity>
          </View>

          {/* Tabs */}
          <View className="flex-row bg-white/5 rounded-2xl p-1 mb-6">
            <TouchableOpacity
              onPress={() => {
                Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
                setActiveTab("feed");
              }}
              className={`flex-1 py-3 rounded-xl ${
                activeTab === "feed" ? "bg-white/10" : ""
              }`}
            >
              <Text
                className={`text-center font-semibold ${
                  activeTab === "feed" ? "text-white" : "text-gray-400"
                }`}
              >
                Feed
              </Text>
            </TouchableOpacity>

            <TouchableOpacity
              onPress={() => {
                Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
                setActiveTab("friends");
              }}
              className={`flex-1 py-3 rounded-xl ${
                activeTab === "friends" ? "bg-white/10" : ""
              }`}
            >
              <Text
                className={`text-center font-semibold ${
                  activeTab === "friends" ? "text-white" : "text-gray-400"
                }`}
              >
                Friends
              </Text>
            </TouchableOpacity>

            <TouchableOpacity
              onPress={() => {
                Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
                setActiveTab("challenges");
              }}
              className={`flex-1 py-3 rounded-xl ${
                activeTab === "challenges" ? "bg-white/10" : ""
              }`}
            >
              <Text
                className={`text-center font-semibold ${
                  activeTab === "challenges" ? "text-white" : "text-gray-400"
                }`}
              >
                Challenges
              </Text>
            </TouchableOpacity>
          </View>

          <Animated.View
            style={{
              opacity: fadeAnim,
              transform: [{ translateY: slideAnim }],
            }}
          >
            {activeTab === "feed" && renderFeedTab()}
            {activeTab === "friends" && renderFriendsTab()}
            {activeTab === "challenges" && renderChallengesTab()}
          </Animated.View>

          {/* Beta tag */}
          <View className="mt-6 mb-10 items-center">
            <View className="bg-blue-500/20 px-3 py-1 rounded-full">
              <Text className="text-blue-400 text-xs">
                Social Features Beta
              </Text>
            </View>
          </View>
        </ScrollView>
      </LinearGradient>

      {/* Friend connect sheet */}
      {showConnectSheet && (
        <View className="absolute inset-0 bg-black/70 justify-end">
          <BlurView intensity={30} tint="dark" className="absolute inset-0" />

          <View className="bg-[#151515] rounded-t-3xl p-6">
            <View className="w-12 h-1 bg-white/20 rounded-full self-center mb-6" />

            <Text className="text-2xl font-bold text-white mb-4">
              Connect with Friends
            </Text>

            <View className="space-y-4 mb-6">
              <TouchableOpacity className="flex-row items-center bg-white/10 p-4 rounded-xl">
                <Ionicons name="mail" size={24} color="#60a5fa" />
                <Text className="text-white ml-3">Invite via Email</Text>
              </TouchableOpacity>

              <TouchableOpacity className="flex-row items-center bg-white/10 p-4 rounded-xl">
                <Ionicons name="logo-facebook" size={24} color="#1877f2" />
                <Text className="text-white ml-3">Find Facebook Friends</Text>
              </TouchableOpacity>

              <TouchableOpacity className="flex-row items-center bg-white/10 p-4 rounded-xl">
                <Ionicons name="person-add" size={24} color="#d8b4fe" />
                <Text className="text-white ml-3">Invite from Contacts</Text>
              </TouchableOpacity>

              <TouchableOpacity className="flex-row items-center bg-white/10 p-4 rounded-xl">
                <Ionicons name="qr-code" size={24} color="#ffffff" />
                <Text className="text-white ml-3">
                  Share Your Profile QR Code
                </Text>
              </TouchableOpacity>
            </View>

            <TouchableOpacity
              onPress={() => setShowConnectSheet(false)}
              className="bg-white/10 p-4 rounded-xl items-center"
            >
              <Text className="text-white font-semibold">Close</Text>
            </TouchableOpacity>
          </View>
        </View>
      )}
    </View>
  );
}
