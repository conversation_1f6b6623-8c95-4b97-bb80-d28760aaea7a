import React, {
  createContext,
  useState,
  useContext,
  ReactNode,
  useEffect,
} from "react";
import AsyncStorage from "@react-native-async-storage/async-storage";
import { useUserProfile } from "./UserProfileContext";
import {
  getAllWeightEntries,
  addWeightEntry as addWeightEntryToSupabase,
  updateWeightEntry as updateWeightEntryInSupabase,
  deleteWeightEntry as deleteWeightEntryFromSupabase,
  clearWeightHistory as clearWeightHistoryFromSupabase,
} from "../services/WeightService";

export interface WeightEntry {
  date: string; // ISO date string YYYY-MM-DD
  weight: number; // Weight in kg
}

interface WeightContextType {
  weightEntries: WeightEntry[];
  addWeightEntry: (weight: number, isMetric: boolean) => Promise<void>;
  updateWeightEntry: (
    date: string,
    weight: number,
    isMetric: boolean
  ) => Promise<void>;
  deleteWeightEntry: (date: string) => Promise<void>;
  clearWeightHistory: () => Promise<void>;
  getWeightTrend: (numberOfMonths?: number) => WeightEntry[];
  calculateWeightChange: () => {
    change: number;
    percentage: number;
    period: string;
  };
  isLoading: boolean;
}

export const WeightContext = createContext<WeightContextType>({
  weightEntries: [],
  addWeightEntry: async () => {},
  updateWeightEntry: async () => {},
  deleteWeightEntry: async () => {},
  clearWeightHistory: async () => {},
  getWeightTrend: () => [],
  calculateWeightChange: () => ({
    change: 0,
    percentage: 0,
    period: "No data",
  }),
  isLoading: true,
});

interface WeightProviderProps {
  children: ReactNode;
}

export function WeightProvider({ children }: WeightProviderProps) {
  const { profile } = useUserProfile();
  const [weightEntries, setWeightEntries] = useState<WeightEntry[]>([]);
  const [isLoading, setIsLoading] = useState(true);

  // Load weight history from Supabase on component mount
  useEffect(() => {
    const loadWeightHistory = async () => {
      try {
        setIsLoading(true);

        // Try to load weight history from Supabase
        const supabaseWeightEntries = await getAllWeightEntries();

        if (supabaseWeightEntries.length > 0) {
          setWeightEntries(supabaseWeightEntries);
        } else {
          // Fallback to local storage if no entries in Supabase
          const storedWeightData = await AsyncStorage.getItem("weightHistory");

          if (storedWeightData) {
            const parsedEntries = JSON.parse(storedWeightData);
            setWeightEntries(parsedEntries);

            // Migrate each weight entry to Supabase
            for (const entry of parsedEntries) {
              await addWeightEntryToSupabase(entry.weight, true); // Assume weight is in kg in local storage
            }

            // Clear local storage after migration
            await AsyncStorage.removeItem("weightHistory");
          } else if (profile.initialized) {
            // Initialize with current profile weight if no history exists
            const today = new Date().toISOString().split("T")[0];
            const initialEntry = { date: today, weight: profile.weight };
            setWeightEntries([initialEntry]);

            // Save initial entry to Supabase
            await addWeightEntryToSupabase(profile.weight, true);
          }
        }
      } catch (error) {
        console.error("Failed to load weight history:", error);
      } finally {
        setIsLoading(false);
      }
    };

    loadWeightHistory();
  }, [profile.initialized, profile.weight]);

  // Add a new weight entry
  const addWeightEntry = async (weight: number, isMetric: boolean) => {
    try {
      // Add to Supabase
      const newEntry = await addWeightEntryToSupabase(weight, isMetric);

      if (newEntry) {
        // Update local state
        const existingEntryIndex = weightEntries.findIndex(
          (entry) => entry.date === newEntry.date
        );

        if (existingEntryIndex >= 0) {
          // Update existing entry
          setWeightEntries((prevEntries) => {
            const updatedEntries = [...prevEntries];
            updatedEntries[existingEntryIndex] = newEntry;

            // Sort entries by date (newest first)
            updatedEntries.sort(
              (a, b) => new Date(b.date).getTime() - new Date(a.date).getTime()
            );

            return updatedEntries;
          });
        } else {
          // Add new entry
          setWeightEntries((prevEntries) => {
            const updatedEntries = [...prevEntries, newEntry];

            // Sort entries by date (newest first)
            updatedEntries.sort(
              (a, b) => new Date(b.date).getTime() - new Date(a.date).getTime()
            );

            return updatedEntries;
          });
        }
      } else {
        // Fallback to local state update if Supabase fails
        const weightInKg = isMetric ? weight : weight * 0.453592;
        const today = new Date().toISOString().split("T")[0];

        // Check if we already have an entry for today
        const existingEntryIndex = weightEntries.findIndex(
          (entry) => entry.date === today
        );

        if (existingEntryIndex >= 0) {
          // Update existing entry
          setWeightEntries((prevEntries) => {
            const updatedEntries = [...prevEntries];
            updatedEntries[existingEntryIndex] = {
              date: today,
              weight: weightInKg,
            };

            // Sort entries by date (newest first)
            updatedEntries.sort(
              (a, b) => new Date(b.date).getTime() - new Date(a.date).getTime()
            );

            return updatedEntries;
          });
        } else {
          // Add new entry
          setWeightEntries((prevEntries) => {
            const updatedEntries = [
              ...prevEntries,
              { date: today, weight: weightInKg },
            ];

            // Sort entries by date (newest first)
            updatedEntries.sort(
              (a, b) => new Date(b.date).getTime() - new Date(a.date).getTime()
            );

            return updatedEntries;
          });
        }
      }
    } catch (error) {
      console.error("Failed to add weight entry:", error);
    }
  };

  // Update an existing weight entry
  const updateWeightEntry = async (
    date: string,
    weight: number,
    isMetric: boolean
  ) => {
    try {
      // Update in Supabase
      const success = await updateWeightEntryInSupabase(date, weight, isMetric);

      // Update local state
      const weightInKg = isMetric ? weight : weight * 0.453592;
      const existingEntryIndex = weightEntries.findIndex(
        (entry) => entry.date === date
      );

      if (existingEntryIndex >= 0) {
        setWeightEntries((prevEntries) => {
          const updatedEntries = [...prevEntries];
          updatedEntries[existingEntryIndex] = { date, weight: weightInKg };
          return updatedEntries;
        });
      } else {
        console.warn(`No weight entry found for date: ${date}`);
      }
    } catch (error) {
      console.error("Failed to update weight entry:", error);
    }
  };

  // Delete a weight entry
  const deleteWeightEntry = async (date: string) => {
    try {
      // Delete from Supabase
      const success = await deleteWeightEntryFromSupabase(date);

      // Update local state
      setWeightEntries((prevEntries) =>
        prevEntries.filter((entry) => entry.date !== date)
      );
    } catch (error) {
      console.error("Failed to delete weight entry:", error);
    }
  };

  // Clear all weight history
  const clearWeightHistory = async () => {
    try {
      // Clear from Supabase
      const success = await clearWeightHistoryFromSupabase();

      // Update local state
      setWeightEntries([]);
    } catch (error) {
      console.error("Failed to clear weight history:", error);
    }
  };

  // Get weight entries for the past X months, sorted by date (oldest to newest)
  const getWeightTrend = (numberOfMonths = 2) => {
    if (weightEntries.length === 0) return [];

    const cutoffDate = new Date();
    cutoffDate.setMonth(cutoffDate.getMonth() - numberOfMonths);
    const cutoffDateStr = cutoffDate.toISOString().split("T")[0];

    return [...weightEntries]
      .filter((entry) => entry.date >= cutoffDateStr)
      .sort((a, b) => new Date(a.date).getTime() - new Date(b.date).getTime());
  };

  // Calculate weight change stats
  const calculateWeightChange = () => {
    if (weightEntries.length < 2) {
      return { change: 0, percentage: 0, period: "No data" };
    }

    // Sort by date in ascending order
    const sortedEntries = [...weightEntries].sort(
      (a, b) => new Date(a.date).getTime() - new Date(b.date).getTime()
    );

    const oldestWeight = sortedEntries[0].weight;
    const currentWeight = sortedEntries[sortedEntries.length - 1].weight;

    const change = currentWeight - oldestWeight;
    const percentage = (change / oldestWeight) * 100;

    // Calculate the period between oldest and newest entry
    const daysDiff = Math.round(
      (new Date(sortedEntries[sortedEntries.length - 1].date).getTime() -
        new Date(sortedEntries[0].date).getTime()) /
        (1000 * 60 * 60 * 24)
    );

    let period;
    if (daysDiff === 0) {
      period = "Today";
    } else if (daysDiff === 1) {
      period = "1 day";
    } else if (daysDiff < 7) {
      period = `${daysDiff} days`;
    } else if (daysDiff < 30) {
      period = `${Math.round(daysDiff / 7)} weeks`;
    } else {
      period = `${Math.round(daysDiff / 30)} months`;
    }

    return { change, percentage, period };
  };

  return (
    <WeightContext.Provider
      value={{
        weightEntries,
        addWeightEntry,
        updateWeightEntry,
        deleteWeightEntry,
        clearWeightHistory,
        getWeightTrend,
        calculateWeightChange,
        isLoading,
      }}
    >
      {children}
    </WeightContext.Provider>
  );
}

export const useWeight = () => useContext(WeightContext);
