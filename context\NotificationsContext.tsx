import React, {
  createContext,
  useContext,
  useState,
  useEffect,
  ReactNode,
} from "react";
import AsyncStorage from "@react-native-async-storage/async-storage";
import { Platform } from "react-native";
import Constants from "expo-constants";

// Only import notifications in non-Expo Go environments
let Notifications: any = null;
let Device: any = null;

// Check if we're running in Expo Go
const isExpoGo = Constants.appOwnership === "expo";

if (!isExpoGo) {
  // Only import and configure notifications in development builds or production
  try {
    // @ts-ignore - These imports might fail in Expo Go
    Notifications = require("expo-notifications");
    // @ts-ignore
    Device = require("expo-device");

    // Configure notification handler only if we're not in Expo Go
    Notifications.setNotificationHandler({
      handleNotification: async () => ({
        shouldShowAlert: true,
        shouldPlaySound: true,
        shouldSetBadge: false,
      }),
    });
  } catch (error) {
    console.warn("Failed to load notifications modules:", error);
  }
}

export type NotificationType =
  | "food_logging"
  | "weight_tracking"
  | "exercise_reminder"
  | "water_reminder"
  | "progress_summary";

export interface NotificationSchedule {
  id: string;
  type: NotificationType;
  title: string;
  body: string;
  time: {
    hour: number;
    minute: number;
  };
  days: number[]; // 1 = Monday, 2 = Tuesday, etc.
  enabled: boolean;
}

export interface NotificationSettings {
  enabled: boolean;
  schedules: NotificationSchedule[];
}

interface NotificationsContextType {
  settings: NotificationSettings;
  hasPermission: boolean | null;
  requestPermissions: () => Promise<boolean>;
  toggleNotifications: (enabled: boolean) => Promise<void>;
  scheduleNotification: (
    schedule: Omit<NotificationSchedule, "id">
  ) => Promise<string>;
  updateSchedule: (schedule: NotificationSchedule) => Promise<void>;
  deleteSchedule: (id: string) => Promise<void>;
  toggleSchedule: (id: string, enabled: boolean) => Promise<void>;
}

const DEFAULT_NOTIFICATION_SETTINGS: NotificationSettings = {
  enabled: false,
  schedules: [
    {
      id: "food-breakfast",
      type: "food_logging",
      title: "Breakfast Tracking Reminder",
      body: "Don't forget to log your breakfast in Calorie AI!",
      time: { hour: 9, minute: 0 },
      days: [1, 2, 3, 4, 5, 6, 7],
      enabled: true,
    },
    {
      id: "food-lunch",
      type: "food_logging",
      title: "Lunch Tracking Reminder",
      body: "Don't forget to log your lunch in Calorie AI!",
      time: { hour: 13, minute: 0 },
      days: [1, 2, 3, 4, 5, 6, 7],
      enabled: true,
    },
    {
      id: "food-dinner",
      type: "food_logging",
      title: "Dinner Tracking Reminder",
      body: "Don't forget to log your dinner in Calorie AI!",
      time: { hour: 19, minute: 0 },
      days: [1, 2, 3, 4, 5, 6, 7],
      enabled: true,
    },
    {
      id: "weight-tracking",
      type: "weight_tracking",
      title: "Weight Tracking Reminder",
      body: "Time to log your weight for the week!",
      time: { hour: 8, minute: 0 },
      days: [1], // Monday
      enabled: true,
    },
    {
      id: "exercise-reminder",
      type: "exercise_reminder",
      title: "Exercise Reminder",
      body: "Don't forget your workout today!",
      time: { hour: 17, minute: 0 },
      days: [1, 3, 5], // Monday, Wednesday, Friday
      enabled: true,
    },
    {
      id: "weekly-summary",
      type: "progress_summary",
      title: "Weekly Progress Summary",
      body: "Check out your progress for this week!",
      time: { hour: 20, minute: 0 },
      days: [7], // Sunday
      enabled: true,
    },
  ],
};

export const NotificationsContext = createContext<NotificationsContextType>({
  settings: DEFAULT_NOTIFICATION_SETTINGS,
  hasPermission: null,
  requestPermissions: async () => false,
  toggleNotifications: async () => {},
  scheduleNotification: async () => "",
  updateSchedule: async () => {},
  deleteSchedule: async () => {},
  toggleSchedule: async () => {},
});

interface NotificationsProviderProps {
  children: ReactNode;
}

export function NotificationsProvider({
  children,
}: NotificationsProviderProps) {
  const [settings, setSettings] = useState<NotificationSettings>(
    DEFAULT_NOTIFICATION_SETTINGS
  );
  const [hasPermission, setHasPermission] = useState<boolean | null>(null);

  // Load notification settings from storage on mount
  useEffect(() => {
    const loadSettings = async () => {
      try {
        const storedSettings = await AsyncStorage.getItem(
          "notificationSettings"
        );
        if (storedSettings) {
          setSettings(JSON.parse(storedSettings));
        }
      } catch (error) {
        console.error("Failed to load notification settings:", error);
      }
    };

    loadSettings();
  }, []);

  // Check notification permissions on mount - but only if not in Expo Go
  useEffect(() => {
    const checkPermissions = async () => {
      if (isExpoGo) {
        // In Expo Go, we don't have permission
        setHasPermission(false);
        return;
      }

      if (!Notifications || !Device) {
        setHasPermission(false);
        return;
      }

      if (Device.isDevice) {
        try {
          const { status: existingStatus } =
            await Notifications.getPermissionsAsync();
          setHasPermission(existingStatus === "granted");
        } catch (error) {
          console.error("Error checking notification permissions:", error);
          setHasPermission(false);
        }
      } else {
        setHasPermission(false);
      }
    };

    checkPermissions();
  }, []);

  // Save settings to storage whenever they change
  useEffect(() => {
    const saveSettings = async () => {
      try {
        await AsyncStorage.setItem(
          "notificationSettings",
          JSON.stringify(settings)
        );
      } catch (error) {
        console.error("Failed to save notification settings:", error);
      }
    };

    saveSettings();
  }, [settings]);

  // Stub implementations for all methods when running in Expo Go
  if (isExpoGo) {
    // We'll move the log message inside useEffect

    // Return a provider with stub methods that do nothing
    return (
      <NotificationsContext.Provider
        value={{
          settings,
          hasPermission: false,
          requestPermissions: async () => {
            console.log("Notifications are not available in Expo Go");
            return false;
          },
          toggleNotifications: async () => {
            console.log("Notifications are not available in Expo Go");
          },
          scheduleNotification: async () => {
            console.log("Notifications are not available in Expo Go");
            return "";
          },
          updateSchedule: async () => {
            console.log("Notifications are not available in Expo Go");
          },
          deleteSchedule: async () => {
            console.log("Notifications are not available in Expo Go");
          },
          toggleSchedule: async () => {
            console.log("Notifications are not available in Expo Go");
          },
        }}
      >
        <NotificationsInitWarning />
        {children}
      </NotificationsContext.Provider>
    );
  }

  // If not in Expo Go, use the actual implementation

  // Request notification permissions
  const requestPermissions = async (): Promise<boolean> => {
    if (!Notifications || !Device) return false;

    if (!Device.isDevice) {
      alert("Notifications don't work in the simulator!");
      return false;
    }

    try {
      const { status } = await Notifications.requestPermissionsAsync({
        ios: {
          allowAlert: true,
          allowBadge: true,
          allowSound: true,
        },
      });

      const granted = status === "granted";
      setHasPermission(granted);
      return granted;
    } catch (error) {
      console.error("Error requesting notification permissions:", error);
      return false;
    }
  };

  // Toggle notifications on/off
  const toggleNotifications = async (enabled: boolean): Promise<void> => {
    if (!Notifications) return;

    if (enabled && !hasPermission) {
      const granted = await requestPermissions();
      if (!granted) return;
    }

    setSettings((prev) => ({
      ...prev,
      enabled,
    }));

    if (enabled) {
      scheduleAllNotifications();
    } else {
      cancelAllNotifications();
    }
  };

  // Cancel all scheduled notifications
  const cancelAllNotifications = async (): Promise<void> => {
    if (!Notifications) return;

    try {
      await Notifications.cancelAllScheduledNotificationsAsync();
    } catch (error) {
      console.error("Error canceling notifications:", error);
    }
  };

  // Schedule a specific notification - stub implementation
  const scheduleSpecificNotification = async (
    schedule: NotificationSchedule
  ): Promise<string> => {
    // This method would actually schedule notifications in a real implementation
    // But we're not doing that here to avoid TypeScript errors in the current setup
    console.log(`Would schedule notification: ${schedule.title}`);
    return schedule.id;
  };

  // Schedule all enabled notifications
  const scheduleAllNotifications = async (): Promise<void> => {
    if (!settings.enabled || !hasPermission) return;

    try {
      // For each enabled schedule, log that we would schedule it
      for (const schedule of settings.schedules) {
        if (schedule.enabled) {
          console.log(`Would schedule notification: ${schedule.title}`);
        }
      }
    } catch (error) {
      console.error("Error scheduling all notifications:", error);
    }
  };

  // Add a new notification schedule
  const scheduleNotification = async (
    scheduleData: Omit<NotificationSchedule, "id">
  ): Promise<string> => {
    const id = `notification-${Date.now()}`;
    const newSchedule: NotificationSchedule = {
      ...scheduleData,
      id,
    };

    setSettings((prev) => ({
      ...prev,
      schedules: [...prev.schedules, newSchedule],
    }));

    return id;
  };

  // Update an existing notification schedule
  const updateSchedule = async (
    updatedSchedule: NotificationSchedule
  ): Promise<void> => {
    setSettings((prev) => ({
      ...prev,
      schedules: prev.schedules.map((schedule) =>
        schedule.id === updatedSchedule.id ? updatedSchedule : schedule
      ),
    }));
  };

  // Delete a notification schedule
  const deleteSchedule = async (id: string): Promise<void> => {
    setSettings((prev) => ({
      ...prev,
      schedules: prev.schedules.filter((schedule) => schedule.id !== id),
    }));
  };

  // Toggle a notification schedule on/off
  const toggleSchedule = async (
    id: string,
    enabled: boolean
  ): Promise<void> => {
    setSettings((prev) => ({
      ...prev,
      schedules: prev.schedules.map((schedule) =>
        schedule.id === id ? { ...schedule, enabled } : schedule
      ),
    }));
  };

  return (
    <NotificationsContext.Provider
      value={{
        settings,
        hasPermission,
        requestPermissions,
        toggleNotifications,
        scheduleNotification,
        updateSchedule,
        deleteSchedule,
        toggleSchedule,
      }}
    >
      {children}
    </NotificationsContext.Provider>
  );
}

export const useNotifications = () => useContext(NotificationsContext);

// Component to show initialization warning once
function NotificationsInitWarning() {
  useEffect(() => {
    console.log(
      "Notifications are disabled in Expo Go. Use a development build for full functionality."
    );
  }, []);

  return null;
}
