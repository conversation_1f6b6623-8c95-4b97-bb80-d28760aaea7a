import React, { useState } from "react";
import { View, Text, TouchableOpacity } from "react-native";
import Animated, { FadeInDown } from "react-native-reanimated";
import { Ionicons } from "@expo/vector-icons";
import Svg, { Circle } from "react-native-svg";
import { useTheme } from "@/context/ThemeContext";
import { getShadowStyle } from "@/utils/shadowStyles";

interface DailyProgressCardProps {
  dateNutrition: {
    calories: number;
    protein: number;
    carbs: number;
    fat: number;
  };
  dateCaloriesBurned: number;
  profile: {
    dailyCalorieGoal: number;
    macroGoals: {
      protein: number;
      carbs: number;
      fat: number;
    };
  };
}

export function DailyProgressCard({
  dateNutrition,
  dateCaloriesBurned,
  profile,
}: DailyProgressCardProps) {
  const { resolvedTheme } = useTheme();
  const [showMacros, setShowMacros] = useState(true);

  // Calculate remaining values
  const remainingCalories =
    profile.dailyCalorieGoal - dateNutrition.calories + dateCaloriesBurned;
  const remainingProtein = profile.macroGoals.protein - dateNutrition.protein;
  const remainingCarbs = profile.macroGoals.carbs - dateNutrition.carbs;
  const remainingFat = profile.macroGoals.fat - dateNutrition.fat;

  // Calorie difference calculation for color determination
  const calorieDifference = dateNutrition.calories - profile.dailyCalorieGoal;

  // Status checks
  const isCaloriesAchieved = Math.abs(calorieDifference) <= 100;
  const isCaloriesWarning = calorieDifference > 100 && calorieDifference <= 300;
  const isCaloriesDanger = calorieDifference > 300;
  const isCaloriesOver = remainingCalories < 0;
  const isProteinOver = remainingProtein < 0;
  const isCarbsOver = remainingCarbs < 0;
  const isFatOver = remainingFat < 0;
  const isProteinAchieved = remainingProtein === 0;
  const isCarbsAchieved = remainingCarbs === 0;
  const isFatAchieved = remainingFat === 0;

  const getStatusText = (isOver: boolean, isAchieved: boolean) => {
    if (isOver) return "Over";
    if (isAchieved) return "Achieved";
    return "Remaining";
  };

  const getCalorieStatusText = () => {
    if (remainingCalories === 0) return "Achieved!";
    if (isCaloriesDanger) return "Well Over";
    if (isCaloriesWarning) return "Over";
    if (isCaloriesAchieved) {
      if (calorieDifference > 0 && calorieDifference <= 100) {
        return "just Over";
      } else {
        return "Close to Target";
      }
    }
    return "Remaining";
  };

  // Calculate progress percentages
  const caloriePercentage = Math.min(
    100,
    (dateNutrition.calories / profile.dailyCalorieGoal) * 100
  );
  const proteinPercentage = Math.min(
    100,
    (dateNutrition.protein / profile.macroGoals.protein) * 100
  );
  const carbsPercentage = Math.min(
    100,
    (dateNutrition.carbs / profile.macroGoals.carbs) * 100
  );
  const fatPercentage = Math.min(
    100,
    (dateNutrition.fat / profile.macroGoals.fat) * 100
  );

  return (
    <View className="mb-2">
      {/* Main Calorie Summary */}
      <View
        className={`rounded-3xl p-6 mb-4 border backdrop-blur-xl ${
          resolvedTheme === "light"
            ? "bg-white border-gray-200/60"
            : "bg-gray-800 border-gray-700"
        }`}
        style={getShadowStyle(resolvedTheme, "card")}
      >
        <View className="flex-row justify-between items-center">
          <View className="flex-1">
            <View>
              <View className="flex-row items-baseline">
                <Text
                  className={`text-5xl font-bold ${
                    resolvedTheme === "light"
                      ? isCaloriesDanger
                        ? "text-red-600"
                        : isCaloriesWarning
                        ? "text-orange-600"
                        : isCaloriesAchieved
                        ? "text-green-600"
                        : "text-gray-800"
                      : isCaloriesDanger
                      ? "text-red-400"
                      : isCaloriesWarning
                      ? "text-yellow-400"
                      : isCaloriesAchieved
                      ? "text-green-400"
                      : "text-white"
                  } mr-1`}
                >
                  {isCaloriesOver
                    ? Math.abs(Math.round(remainingCalories))
                    : Math.max(0, Math.round(remainingCalories))}
                </Text>
                <Text
                  className={`text-lg ${
                    resolvedTheme === "light" ? "text-gray-600" : "text-white"
                  }`}
                >
                  kcal
                </Text>
              </View>
              <Text
                className={`text-lg pl-1 ${
                  resolvedTheme === "light"
                    ? isCaloriesDanger
                      ? "text-red-600"
                      : isCaloriesWarning
                      ? "text-orange-600"
                      : isCaloriesAchieved
                      ? "text-green-600"
                      : "text-gray-700"
                    : isCaloriesDanger
                    ? "text-red-400"
                    : isCaloriesWarning
                    ? "text-yellow-400"
                    : isCaloriesAchieved
                    ? "text-green-400"
                    : "text-gray-400"
                }`}
              >
                {getCalorieStatusText()}
              </Text>
            </View>
          </View>

          <View
            className={`w-24 h-24 rounded-full items-center justify-center ${
              resolvedTheme === "light" ? "bg-gray-100/60" : "bg-black/20"
            }`}
          >
            <Text
              className={`text-2xl font-bold ${
                resolvedTheme === "light"
                  ? isCaloriesDanger
                    ? "text-red-600"
                    : isCaloriesWarning
                    ? "text-orange-600"
                    : isCaloriesAchieved
                    ? "text-green-600"
                    : "text-gray-800"
                  : isCaloriesDanger
                  ? "text-red-400"
                  : isCaloriesWarning
                  ? "text-yellow-400"
                  : isCaloriesAchieved
                  ? "text-green-400"
                  : "text-white"
              } z-10`}
            >
              {Math.round(caloriePercentage)}%
            </Text>

            {/* SVG for the progress arc */}
            <Svg width={96} height={96} style={{ position: "absolute" }}>
              {/* Background circle */}
              <Circle
                cx={48}
                cy={48}
                r={44}
                strokeWidth={4}
                stroke={
                  resolvedTheme === "light"
                    ? "rgba(156, 163, 175, 0.3)"
                    : "rgba(255, 255, 255, 0.2)"
                }
                fill="transparent"
              />

              {/* Progress arc */}
              <Circle
                cx={48}
                cy={48}
                r={44}
                strokeWidth={4}
                stroke={
                  resolvedTheme === "light"
                    ? isCaloriesDanger
                      ? "#dc2626"
                      : isCaloriesWarning
                      ? "#ea580c"
                      : isCaloriesAchieved
                      ? "#16a34a"
                      : "#4b5563"
                    : isCaloriesDanger
                    ? "#FF6B6B"
                    : isCaloriesWarning
                    ? "#FFC107"
                    : isCaloriesAchieved
                    ? "#4CAF50"
                    : "#FFFFFF"
                }
                fill="transparent"
                strokeDasharray={`${2 * Math.PI * 44}`}
                strokeDashoffset={
                  dateNutrition.calories >= profile.dailyCalorieGoal
                    ? 0
                    : 2 *
                      Math.PI *
                      44 *
                      (1 - dateNutrition.calories / profile.dailyCalorieGoal)
                }
                strokeLinecap="round"
                transform={`rotate(-90, 48, 48)`}
              />
            </Svg>
          </View>
        </View>
      </View>

      {/* Macro Tracking Toggle */}
      <View
        className={`flex-row justify-between items-center ${
          showMacros ? "mb-2" : "mb-0"
        }`}
      >
        <Text
          className={`text-xl font-bold ${
            resolvedTheme === "light" ? "text-gray-800" : "text-white"
          }`}
        ></Text>
        <TouchableOpacity
          onPress={() => setShowMacros(!showMacros)}
          className={`p-2 rounded-full ${
            resolvedTheme === "light" ? "bg-gray-200/60" : "bg-gray-700"
          }`}
        >
          <Ionicons
            name={showMacros ? "chevron-up" : "chevron-down"}
            size={16}
            color={resolvedTheme === "light" ? "#4b5563" : "white"}
          />
        </TouchableOpacity>
      </View>

      {showMacros && (
        <View className="flex-row justify-between mb-6">
          {/* Protein Card */}
          <View
            className={`rounded-2xl p-4 w-[32%] border ${
              resolvedTheme === "light"
                ? "bg-white border-gray-200/50"
                : "bg-gray-800 border-gray-700"
            }`}
            style={getShadowStyle(resolvedTheme, "card")}
          >
            <View className="flex-row justify-between items-center mb-2">
              <Ionicons name="barbell-outline" size={20} color="#F44336" />
              <Text
                className={`text-sm ${
                  resolvedTheme === "light" ? "text-gray-600" : "text-gray-400"
                }`}
              >
                Protein
              </Text>
            </View>
            <Text
              className={`text-xl font-bold ${
                resolvedTheme === "light"
                  ? isProteinOver
                    ? "text-red-600"
                    : isProteinAchieved
                    ? "text-green-600"
                    : "text-gray-800"
                  : isProteinOver
                  ? "text-red-400"
                  : isProteinAchieved
                  ? "text-green-400"
                  : "text-white"
              }`}
            >
              {isProteinOver
                ? Math.abs(Math.round(remainingProtein))
                : Math.max(0, Math.round(remainingProtein))}
              g
            </Text>
            <Text
              className={`text-[0.8rem] ${
                resolvedTheme === "light"
                  ? isProteinOver
                    ? "text-red-600"
                    : isProteinAchieved
                    ? "text-green-600"
                    : "text-gray-700"
                  : isProteinOver
                  ? "text-red-400"
                  : isProteinAchieved
                  ? "text-green-400"
                  : "text-gray-400"
              }`}
            >
              {getStatusText(isProteinOver, isProteinAchieved)}
            </Text>
            <View
              className={`h-1 rounded-full mt-2 ${
                resolvedTheme === "light" ? "bg-gray-200/60" : "bg-gray-700"
              }`}
            >
              <View
                className={`h-full ${
                  resolvedTheme === "light"
                    ? isProteinOver
                      ? "bg-red-400/80"
                      : isProteinAchieved
                      ? "bg-green-400/80"
                      : "bg-gray-400/60"
                    : isProteinOver
                    ? "bg-red-400/50"
                    : isProteinAchieved
                    ? "bg-green-400/50"
                    : "bg-gray-400/50"
                } rounded-full`}
                style={{
                  width: `${isProteinOver ? 100 : proteinPercentage}%`,
                }}
              />
            </View>
          </View>

          {/* Carbs Card */}
          <View
            className={`rounded-2xl p-4 w-[32%] border ${
              resolvedTheme === "light"
                ? "bg-white border-gray-200/50"
                : "bg-gray-800 border-gray-700"
            }`}
            style={getShadowStyle(resolvedTheme, "card")}
          >
            <View className="flex-row justify-between items-center mb-2">
              <Ionicons name="leaf-outline" size={20} color="#4CAF50" />
              <Text
                className={`text-sm ${
                  resolvedTheme === "light" ? "text-gray-600" : "text-gray-400"
                }`}
              >
                Carbs
              </Text>
            </View>
            <Text
              className={`text-xl font-bold ${
                resolvedTheme === "light"
                  ? isCarbsOver
                    ? "text-red-600"
                    : isCarbsAchieved
                    ? "text-green-600"
                    : "text-gray-800"
                  : isCarbsOver
                  ? "text-red-400"
                  : isCarbsAchieved
                  ? "text-green-400"
                  : "text-white"
              }`}
            >
              {isCarbsOver
                ? Math.abs(Math.round(remainingCarbs))
                : Math.max(0, Math.round(remainingCarbs))}
              g
            </Text>
            <Text
              className={`text-[0.8rem] ${
                resolvedTheme === "light"
                  ? isCarbsOver
                    ? "text-red-600"
                    : isCarbsAchieved
                    ? "text-green-600"
                    : "text-gray-700"
                  : isCarbsOver
                  ? "text-red-400"
                  : isCarbsAchieved
                  ? "text-green-400"
                  : "text-gray-400"
              }`}
            >
              {getStatusText(isCarbsOver, isCarbsAchieved)}
            </Text>
            <View
              className={`h-1 rounded-full mt-2 ${
                resolvedTheme === "light" ? "bg-gray-200/60" : "bg-gray-700"
              }`}
            >
              <View
                className={`h-full ${
                  resolvedTheme === "light"
                    ? isCarbsOver
                      ? "bg-red-400/80"
                      : isCarbsAchieved
                      ? "bg-green-400/80"
                      : "bg-gray-400/60"
                    : isCarbsOver
                    ? "bg-red-400/50"
                    : isCarbsAchieved
                    ? "bg-green-400/50"
                    : "bg-gray-400/50"
                } rounded-full`}
                style={{ width: `${isCarbsOver ? 100 : carbsPercentage}%` }}
              />
            </View>
          </View>

          {/* Fats Card */}
          <View
            className={`rounded-2xl p-4 w-[32%] border ${
              resolvedTheme === "light"
                ? "bg-white border-gray-200/50"
                : "bg-gray-800 border-gray-700"
            }`}
            style={getShadowStyle(resolvedTheme, "card")}
          >
            <View className="flex-row justify-between items-center mb-2">
              <Ionicons name="water-outline" size={20} color="#2196F3" />
              <Text
                className={`text-sm ${
                  resolvedTheme === "light" ? "text-gray-600" : "text-gray-400"
                }`}
              >
                Fats
              </Text>
            </View>
            <Text
              className={`text-xl font-bold ${
                resolvedTheme === "light"
                  ? isFatOver
                    ? "text-red-600"
                    : isFatAchieved
                    ? "text-green-600"
                    : "text-gray-800"
                  : isFatOver
                  ? "text-red-400"
                  : isFatAchieved
                  ? "text-green-400"
                  : "text-white"
              }`}
            >
              {isFatOver
                ? Math.abs(Math.round(remainingFat))
                : Math.max(0, Math.round(remainingFat))}
              g
            </Text>
            <Text
              className={`text-[0.8rem] ${
                resolvedTheme === "light"
                  ? isFatOver
                    ? "text-red-600"
                    : isFatAchieved
                    ? "text-green-600"
                    : "text-gray-700"
                  : isFatOver
                  ? "text-red-400"
                  : isFatAchieved
                  ? "text-green-400"
                  : "text-gray-400"
              }`}
            >
              {getStatusText(isFatOver, isFatAchieved)}
            </Text>
            <View
              className={`h-1 rounded-full mt-2 ${
                resolvedTheme === "light" ? "bg-gray-200/60" : "bg-gray-700"
              }`}
            >
              <View
                className={`h-full ${
                  resolvedTheme === "light"
                    ? isFatOver
                      ? "bg-red-400/80"
                      : isFatAchieved
                      ? "bg-green-400/80"
                      : "bg-gray-400/60"
                    : isFatOver
                    ? "bg-red-400/50"
                    : isFatAchieved
                    ? "bg-green-400/50"
                    : "bg-gray-400/50"
                } rounded-full`}
                style={{ width: `${isFatOver ? 100 : fatPercentage}%` }}
              />
            </View>
          </View>
        </View>
      )}
    </View>
  );
}
