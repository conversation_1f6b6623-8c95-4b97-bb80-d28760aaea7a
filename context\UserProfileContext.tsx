import React, {
  createContext,
  useState,
  useContext,
  ReactNode,
  useEffect,
} from "react";
import AsyncStorage from "@react-native-async-storage/async-storage";
import {
  getProfile,
  saveProfile as saveProfileToSupabase,
} from "../services/ProfileService";
import { restoreSessionFromTokens } from "../config/supabase";
import { useAuth } from "./AuthContext";

// Activity level multipliers for TDEE calculation
const ACTIVITY_MULTIPLIERS = {
  sedentary: 1.2, // Little or no exercise
  lightlyActive: 1.375, // Light exercise 1-3 days/week
  moderatelyActive: 1.55, // Moderate exercise 3-5 days/week
  veryActive: 1.725, // Hard exercise 6-7 days/week
  extraActive: 1.9, // Very hard exercise & physical job or training twice a day
};

// Goal type calorie adjustments (calories per day)
const GOAL_ADJUSTMENTS = {
  lose: -500, // Lose about 1 pound per week
  maintain: 0, // Maintain current weight
  gain: 500, // Gain about 1 pound per week
};

export type ActivityLevel = keyof typeof ACTIVITY_MULTIPLIERS;
export type GoalType = keyof typeof GOAL_ADJUSTMENTS;

export interface UserProfile {
  weight: number; // kg
  height: number; // cm
  age: number;
  gender: "male" | "female" | "other";
  activityLevel: ActivityLevel;
  goalType: GoalType;
  targetWeight?: number;
  weightChangeRate?: number; // lbs per week
  dailyCalorieGoal: number;
  macroGoals: {
    protein: number; // grams
    carbs: number; // grams
    fat: number; // grams
  };
  initialized: boolean;
}

const DEFAULT_PROFILE: UserProfile = {
  weight: 70, // kg
  height: 170, // cm
  age: 30,
  gender: "male",
  activityLevel: "moderatelyActive",
  goalType: "maintain",
  dailyCalorieGoal: 2000,
  macroGoals: {
    protein: 150, // grams
    carbs: 200, // grams
    fat: 60, // grams
  },
  initialized: false,
};

interface UserProfileContextType {
  profile: UserProfile;
  updateProfile: (update: Partial<UserProfile>) => void;
  calculateBMR: () => number;
  calculateTDEE: () => number;
  calculateCalorieGoal: () => number;
  calculateMacros: (calorieGoal: number) => {
    protein: number;
    carbs: number;
    fat: number;
  };
  saveProfile: () => Promise<void>;
  resetProfile: () => void;
  isProfileComplete: boolean;
  isLoading: boolean;
}

export const UserProfileContext = createContext<UserProfileContextType>({
  profile: DEFAULT_PROFILE,
  updateProfile: () => {},
  calculateBMR: () => 0,
  calculateTDEE: () => 0,
  calculateCalorieGoal: () => 0,
  calculateMacros: () => ({ protein: 0, carbs: 0, fat: 0 }),
  saveProfile: async () => {},
  resetProfile: () => {},
  isProfileComplete: false,
  isLoading: true,
});

interface UserProfileProviderProps {
  children: ReactNode;
}

export function UserProfileProvider({ children }: UserProfileProviderProps) {
  const [profile, setProfile] = useState<UserProfile>(DEFAULT_PROFILE);
  const [isProfileComplete, setIsProfileComplete] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const { isAuthenticated, isLoading: authLoading } = useAuth();

  // Initialize and load profile data when authentication is complete
  useEffect(() => {
    // Don't try to load profile until auth is done loading
    if (authLoading) {
      return;
    }

    const loadProfile = async () => {
      try {
        setIsLoading(true);

        if (!isAuthenticated) {
          console.error("Failed to authenticate user");
          setIsLoading(false);
          return;
        }

        // Try to load profile from Supabase
        const supabaseProfile = await getProfile();

        if (supabaseProfile) {
          setProfile(supabaseProfile);
          setIsProfileComplete(supabaseProfile.initialized);
        } else {
          // If no profile in Supabase, try to get from local storage as a fallback
          const storedProfile = await AsyncStorage.getItem("userProfile");
          if (storedProfile) {
            const parsedProfile = JSON.parse(storedProfile);
            setProfile(parsedProfile);
            setIsProfileComplete(parsedProfile.initialized);

            // Migrate local data to Supabase
            await saveProfileToSupabase(parsedProfile);

            // Clear local storage after migration
            await AsyncStorage.removeItem("userProfile");
          }
        }
      } catch (error) {
        console.error("Failed to load profile data:", error);
      } finally {
        setIsLoading(false);
      }
    };

    loadProfile();
  }, [isAuthenticated, authLoading]);

  // Calculate Basal Metabolic Rate (BMR) using Harris-Benedict equation
  const calculateBMR = () => {
    if (profile.gender === "male") {
      // Men: BMR = 66 + (13.7 × weight in kg) + (5 × height in cm) - (6.8 × age in years)
      return (
        66 + 13.7 * profile.weight + 5 * profile.height - 6.8 * profile.age
      );
    } else {
      // Women: BMR = 655 + (9.6 × weight in kg) + (1.8 × height in cm) - (4.7 × age in years)
      return (
        655 + 9.6 * profile.weight + 1.8 * profile.height - 4.7 * profile.age
      );
    }
  };

  // Calculate Total Daily Energy Expenditure (TDEE)
  const calculateTDEE = () => {
    const bmr = calculateBMR();
    return bmr * ACTIVITY_MULTIPLIERS[profile.activityLevel];
  };

  // Calculate daily calorie goal based on TDEE and goal type
  const calculateCalorieGoal = () => {
    const tdee = calculateTDEE();
    const goalAdjustment = GOAL_ADJUSTMENTS[profile.goalType];

    // Apply custom weight change rate if specified
    if (profile.weightChangeRate) {
      // Each pound per week is roughly 500 calories per day
      return (
        tdee +
        (profile.goalType === "maintain" ? 0 : profile.weightChangeRate * 500)
      );
    }

    return tdee + goalAdjustment;
  };

  // Calculate macronutrient targets based on calorie goal
  const calculateMacros = (calorieGoal: number) => {
    // Default macronutrient distribution:
    // Protein: 30% of calories (4 calories per gram)
    // Fat: 25% of calories (9 calories per gram)
    // Carbs: 45% of calories (4 calories per gram)

    const protein = Math.round((calorieGoal * 0.3) / 4);
    const fat = Math.round((calorieGoal * 0.25) / 9);
    const carbs = Math.round((calorieGoal * 0.45) / 4);

    return { protein, carbs, fat };
  };

  // Update profile with new values and recalculate goals
  const updateProfile = (update: Partial<UserProfile>) => {
    setProfile((prev) => {
      const newProfile = { ...prev, ...update };

      // Recalculate calorie goal and macros if weight, height, age, gender, activity level, or goal type change
      if (
        update.weight !== undefined ||
        update.height !== undefined ||
        update.age !== undefined ||
        update.gender !== undefined ||
        update.activityLevel !== undefined ||
        update.goalType !== undefined ||
        update.weightChangeRate !== undefined
      ) {
        const newCalorieGoal = calculateCalorieGoal();
        const newMacros = calculateMacros(newCalorieGoal);

        newProfile.dailyCalorieGoal = Math.round(newCalorieGoal);
        newProfile.macroGoals = newMacros;
      }

      return newProfile;
    });

    // Update profile completion status
    setIsProfileComplete(true);
  };

  // Save profile data to Supabase
  const saveProfile = async () => {
    try {
      // Always ensure initialized is set to true when saving
      const updatedProfile = { ...profile, initialized: true };

      // Save to Supabase
      const success = await saveProfileToSupabase(updatedProfile);

      if (success) {
        setProfile(updatedProfile);
        setIsProfileComplete(true);
      } else {
        // Fallback to local storage if Supabase save fails
        await AsyncStorage.setItem(
          "userProfile",
          JSON.stringify(updatedProfile)
        );
        setProfile(updatedProfile);
        setIsProfileComplete(true);
      }
    } catch (error) {
      console.error("Failed to save profile data:", error);

      // Fallback to local storage
      try {
        const updatedProfile = { ...profile, initialized: true };
        await AsyncStorage.setItem(
          "userProfile",
          JSON.stringify(updatedProfile)
        );
        setProfile(updatedProfile);
        setIsProfileComplete(true);
      } catch (storageError) {
        console.error("Failed to save profile to local storage:", storageError);
      }
    }
  };

  // Reset profile to default values
  const resetProfile = () => {
    setProfile(DEFAULT_PROFILE);
    setIsProfileComplete(false);

    // Clear both Supabase (through update) and local storage
    saveProfileToSupabase(DEFAULT_PROFILE);
    AsyncStorage.removeItem("userProfile");
  };

  return (
    <UserProfileContext.Provider
      value={{
        profile,
        updateProfile,
        calculateBMR,
        calculateTDEE,
        calculateCalorieGoal,
        calculateMacros,
        saveProfile,
        resetProfile,
        isProfileComplete,
        isLoading,
      }}
    >
      {children}
    </UserProfileContext.Provider>
  );
}

export const useUserProfile = () => useContext(UserProfileContext);
