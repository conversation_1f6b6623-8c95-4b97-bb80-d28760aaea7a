import React from "react";
import { View, Text, ActivityIndicator } from "react-native";
import Animated, {
  FadeInDown,
  LinearTransition,
} from "react-native-reanimated";
import { Image } from "expo-image";
import { useTheme } from "@/context/ThemeContext";

interface AnalyzingCardProps {
  selectedImage: string;
  isLoading: boolean;
}

export function AnalyzingCard({
  selectedImage,
  isLoading,
}: AnalyzingCardProps) {
  const { resolvedTheme } = useTheme();

  // Only render when we're in the analyzing state (loading) with a selected image
  // Don't render when analysis is complete (nutritionInfo exists and isLoading is false)
  if (!isLoading || !selectedImage) return null;

  // Current timestamp to show in the card
  const now = new Date();
  const formattedTime = now.toLocaleTimeString([], {
    hour: "2-digit",
    minute: "2-digit",
  });

  return (
    <Animated.View
      entering={FadeInDown.duration(500).delay(400)}
      layout={LinearTransition.springify()}
      className={`rounded-2xl overflow-hidden flex-row border mb-3 ${
        resolvedTheme === "light"
          ? "bg-white border-gray-200"
          : "bg-gray-800 border-gray-700"
      }`}
    >
      <Image
        source={{ uri: selectedImage }}
        style={{ width: 96, height: "100%" }}
        contentFit="cover"
        transition={100}
        cachePolicy="memory-disk"
        placeholder={{
          uri: "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNk+P+/HgAFeAJ5jR4WaAAAAABJRU5ErkJggg==",
        }}
      />
      <View className="flex-1 py-3 px-3">
        <Text
          className={`${
            resolvedTheme === "light" ? "text-gray-700" : "text-white"
          } font-semibold`}
        >
          Analyzing food...
        </Text>
        <View className="flex-row justify-between items-center mt-1">
          <View className="flex-row items-center">
            <ActivityIndicator
              size="small"
              color={resolvedTheme === "light" ? "#262626" : "#FFFFFF"}
              style={{ marginRight: 6 }}
            />
            <Text
              className={`${
                resolvedTheme === "light" ? "text-black" : "text-gray-400"
              } text-sm ml-1`}
            >
              Analyzing
            </Text>
          </View>
          <Text
            className={`text-sm ${
              resolvedTheme === "light" ? "text-black" : "text-gray-500"
            }`}
          >
            {formattedTime}
          </Text>
        </View>

        <View className="flex-row mt-1">
          <View
            className={`rounded-full px-2 py-0.5 mr-2 ${
              resolvedTheme === "light" ? "bg-gray-100" : "bg-gray-500/20"
            }`}
          >
            <Text
              className={`text-sm ${
                resolvedTheme === "light" ? "text-black" : "text-gray-300"
              }`}
            >
              Processing...
            </Text>
          </View>
        </View>
      </View>
    </Animated.View>
  );
}
