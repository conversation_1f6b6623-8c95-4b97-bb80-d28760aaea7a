import { Tabs } from "expo-router";
import React from "react";
import { Platform, View } from "react-native";
import { useColorScheme as useNativeColorScheme } from "react-native";
import { Colors } from "@/constants/Colors";
import { FontAwesome } from "@expo/vector-icons";
import "../../global.css";
import { SafeAreaView } from "react-native-safe-area-context";

import { HapticTab } from "@/components/HapticTab";
import { IconSymbol } from "@/components/ui/IconSymbol";
import TabBarBackground from "@/components/ui/TabBarBackground";
import { useTheme } from "@/context/ThemeContext";

function TabBarIcon(props: {
  name: React.ComponentProps<typeof FontAwesome>["name"];
  color: string;
}) {
  return <FontAwesome size={28} style={{ marginBottom: -3 }} {...props} />;
}

export default function TabLayout() {
  const nativeColorScheme = useNativeColorScheme();
  const { resolvedTheme } = useTheme();

  return (
    <View className="flex-1">
      <SafeAreaView
        className={`flex-1 ${
          resolvedTheme === "dark" ? "bg-gray-950" : "bg-gray-50"
        }`}
        edges={["top"]}
      >
        <Tabs
          key={resolvedTheme}
          screenOptions={{
            tabBarActiveTintColor: Colors[resolvedTheme].tint,
            headerShown: false,
            tabBarButton: HapticTab,
            tabBarBackground: TabBarBackground,
            tabBarStyle: Platform.select({
              ios: {
                // Keep position absolute but now we'll show our gradient
                position: "absolute",
                backgroundColor: "transparent",
              },
              default: {
                backgroundColor: "transparent", // Make transparent to show gradient on Android
              },
            }),
            tabBarLabelStyle: {
              fontWeight: "600",
            },
          }}
        >
          <Tabs.Screen
            name="index"
            options={{
              title: "Home",
              tabBarIcon: ({ color }) => (
                <TabBarIcon name="home" color={color} />
              ),
            }}
          />
          <Tabs.Screen
            name="history"
            options={{
              title: "Analytics",
              tabBarIcon: ({ color }) => (
                <TabBarIcon name="bar-chart-o" color={color} />
              ),
            }}
          />
          <Tabs.Screen
            name="explore"
            options={{
              title: "Explore",
              tabBarIcon: ({ color }) => (
                <IconSymbol size={28} name="paperplane.fill" color={color} />
              ),
            }}
          />
          <Tabs.Screen
            name="settings"
            options={{
              title: "Settings",
              tabBarIcon: ({ color }) => (
                <TabBarIcon name="cog" color={color} />
              ),
            }}
          />
        </Tabs>
      </SafeAreaView>
    </View>
  );
}
