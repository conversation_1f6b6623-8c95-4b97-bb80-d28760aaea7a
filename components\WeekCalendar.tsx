import React, { useRef, useEffect } from "react";
import {
  View,
  Text,
  TouchableOpacity,
  ScrollView,
  Dimensions,
} from "react-native";
import Animated, { FadeInDown } from "react-native-reanimated";
import Svg, { Circle } from "react-native-svg";
import * as Haptics from "expo-haptics";
import { useTheme } from "@/context/ThemeContext";

interface WeekCalendarProps {
  selectedDate: string;
  onDateSelect: (dateString: string) => void;
  getDailyNutrition: (date: string) => { calories: number };
  profile: {
    dailyCalorieGoal: number;
  };
}

export function WeekCalendar({
  selectedDate,
  onDateSelect,
  getDailyNutrition,
  profile,
}: WeekCalendarProps) {
  const { resolvedTheme } = useTheme();
  const windowWidth = Dimensions.get("window").width;
  const calendarScrollRef = useRef<ScrollView>(null);

  const selectDate = (dateString: string) => {
    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
    onDateSelect(dateString);
  };

  const scrollToCurrentWeek = () => {
    setTimeout(() => {
      if (calendarScrollRef.current) {
        // Calculate which week to scroll to based on selected date
        const today = new Date();
        const todayISO = today.toISOString().split("T")[0];
        const selectedDateObj = new Date(selectedDate + "T00:00:00.000Z");
        const currentDate = new Date(todayISO + "T00:00:00.000Z");

        // Calculate days difference between selected date and today
        const diffTime = currentDate.getTime() - selectedDateObj.getTime();
        const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

        // Calculate which week to scroll to (0-4, where 4 is current week)
        // Each week is 7 days, so divide by 7 to get week number
        const weekOffset = Math.min(4, Math.max(0, Math.floor(diffDays / 7)));

        // Scroll to the appropriate week
        const scrollToX = windowWidth * (4 - weekOffset);
        calendarScrollRef.current.scrollTo({ x: scrollToX, animated: false });
      }
    }, 100);
  };

  useEffect(() => {
    scrollToCurrentWeek();
  }, [selectedDate]);

  return (
    <Animated.View
      entering={FadeInDown.duration(500).delay(100)}
      className="mb-6"
    >
      <View className="mx-1 overflow-hidden">
        <View style={{ width: windowWidth }}>
          <ScrollView
            ref={calendarScrollRef}
            horizontal
            showsHorizontalScrollIndicator={false}
            className="flex-row pb-2"
            snapToInterval={windowWidth}
            decelerationRate="fast"
            snapToAlignment="start"
            pagingEnabled={true}
            bounces={false}
            onLayout={() => {
              // This ensures we scroll to current week when component fully renders
              scrollToCurrentWeek();
            }}
          >
            {(() => {
              // Get current date info
              const today = new Date();
              const todayISO = today.toISOString().split("T")[0];

              // Calculate date for the most recent Monday (start of the week)
              const currentDayOfWeek = today.getDay(); // 0 = Sunday, 1 = Monday, etc.
              const daysToMostRecentMonday =
                currentDayOfWeek === 0 ? 6 : currentDayOfWeek - 1;

              // Create array to hold all weeks
              const allWeeks = [];

              // Get the start of current week (Monday)
              const currentWeekMonday = new Date(today);
              currentWeekMonday.setDate(
                today.getDate() - daysToMostRecentMonday
              );

              // Generate weeks ONLY for current and PAST weeks (not future)
              // We're building 5 weeks: current week + 4 previous weeks
              for (let weekOffset = 4; weekOffset >= 0; weekOffset--) {
                // Calculate Monday of each week - going backward in time
                const mondayOfWeek = new Date(currentWeekMonday);
                mondayOfWeek.setDate(
                  currentWeekMonday.getDate() - weekOffset * 7
                );

                // Create array for this week's days
                const daysOfWeek = [];

                // Generate the 7 days of the week (Monday to Sunday)
                for (let dayInWeek = 0; dayInWeek < 7; dayInWeek++) {
                  const date = new Date(mondayOfWeek);
                  date.setDate(mondayOfWeek.getDate() + dayInWeek);

                  // Format date info
                  const dayName = date.toLocaleDateString("en-US", {
                    weekday: "short",
                  });
                  const dayNum = date.getDate();
                  const dateString = date.toISOString().split("T")[0];
                  const isSelected = dateString === selectedDate;

                  // Normalize dates to compare only the date part without time
                  const normalizedDate = new Date(
                    dateString + "T00:00:00.000Z"
                  );
                  const normalizedToday = new Date(todayISO + "T00:00:00.000Z");

                  // Mark as future ONLY if the date is truly in the future compared to today
                  const isFutureDate = normalizedDate > normalizedToday;
                  const isToday = dateString === todayISO;

                  // Get nutrition data
                  const dayNutrition = getDailyNutrition(dateString);

                  // Calculate the difference between calories consumed and goal
                  const calorieDifference =
                    dayNutrition.calories - profile.dailyCalorieGoal;

                  // Determine color based on calorie difference, not macros
                  // Match the colors with the progress bar
                  let progressColor;
                  if (dayNutrition.calories === 0) {
                    // No calories logged
                    progressColor = "rgba(255, 255, 255, 0.8)"; // Default white
                  } else if (calorieDifference === 0) {
                    // Exactly at goal
                    progressColor = "rgba(76, 175, 80, 0.8)"; // Green for achieved
                  } else if (Math.abs(calorieDifference) <= 100) {
                    // Within 100 calories of goal (under or over)
                    progressColor = "rgba(76, 175, 80, 0.8)"; // Green for close to goal
                  } else if (
                    calorieDifference > 100 &&
                    calorieDifference <= 300
                  ) {
                    // 100-300 calories over goal
                    progressColor = "rgba(255, 193, 7, 0.8)"; // Yellow for warning
                  } else if (calorieDifference > 300) {
                    // More than 300 calories over goal
                    progressColor = "rgba(255, 107, 107, 0.8)"; // Red for well over
                  } else {
                    // More than 100 calories under goal
                    progressColor = "rgba(255, 255, 255, 0.8)"; // Default white
                  }

                  // Calculate progress percentage
                  const progressPercentage = Math.min(
                    100,
                    (dayNutrition.calories / profile.dailyCalorieGoal) * 100
                  );

                  // Calculate SVG values
                  const circumference = 2 * Math.PI * 18;
                  const dashOffset =
                    circumference * (1 - progressPercentage / 100);

                  // Create day component with fixed width
                  daysOfWeek.push(
                    <View
                      key={dateString}
                      style={{ width: (windowWidth - 39) / 7 }}
                      className="items-center"
                    >
                      <TouchableOpacity
                        onPress={() => !isFutureDate && selectDate(dateString)}
                        disabled={isFutureDate}
                        className={`items-center ${
                          isSelected
                            ? "opacity-100"
                            : isToday
                            ? "opacity-100"
                            : isFutureDate
                            ? "opacity-30"
                            : resolvedTheme === "light"
                            ? "opacity-85"
                            : "opacity-70"
                        }`}
                      >
                        <Text
                          className={`text-sm mb-1 ${
                            isToday
                              ? resolvedTheme === "light"
                                ? "text-gray-800 font-bold"
                                : "text-white font-bold"
                              : resolvedTheme === "light"
                              ? "text-gray-700"
                              : "text-gray-400"
                          }`}
                        >
                          {dayName}
                        </Text>

                        <View className="relative w-11 h-11 items-center justify-center">
                          {/* SVG for progress circle */}
                          <Svg
                            width={44}
                            height={44}
                            style={{ position: "absolute" }}
                          >
                            {/* Background circle */}
                            <Circle
                              cx={22}
                              cy={22}
                              r={18}
                              strokeWidth={3}
                              stroke={
                                resolvedTheme === "light"
                                  ? "rgba(156, 163, 175, 0.4)"
                                  : "rgba(255, 255, 255, 0.1)"
                              }
                              fill="transparent"
                            />

                            {/* Progress arc - only show for days with activity */}
                            {dayNutrition.calories > 0 && (
                              <Circle
                                cx={22}
                                cy={22}
                                r={18}
                                strokeWidth={isSelected ? 4 : 3}
                                stroke={
                                  resolvedTheme === "light"
                                    ? dayNutrition.calories === 0
                                      ? "rgba(156, 163, 175, 0.6)"
                                      : calorieDifference === 0
                                      ? "rgba(34, 197, 94, 0.9)"
                                      : Math.abs(calorieDifference) <= 100
                                      ? "rgba(34, 197, 94, 0.9)"
                                      : calorieDifference > 100 &&
                                        calorieDifference <= 300
                                      ? "rgba(234, 88, 12, 0.9)"
                                      : calorieDifference > 300
                                      ? "rgba(220, 38, 38, 0.9)"
                                      : "rgba(156, 163, 175, 0.7)"
                                    : progressColor
                                }
                                fill="transparent"
                                strokeDasharray={circumference}
                                strokeDashoffset={dashOffset}
                                strokeLinecap="round"
                                transform="rotate(-90, 22, 22)"
                              />
                            )}
                          </Svg>

                          {/* Day number - keeping the original styling */}
                          <View
                            className={`w-8 h-8 rounded-full items-center justify-center ${
                              isSelected
                                ? resolvedTheme === "light"
                                  ? "bg-gray-800"
                                  : "bg-[#e5e5e5]"
                                : ""
                            }`}
                          >
                            <Text
                              className={`${
                                isSelected
                                  ? resolvedTheme === "light"
                                    ? "text-white"
                                    : "text-black"
                                  : resolvedTheme === "light"
                                  ? "text-gray-800"
                                  : "text-white"
                              } font-medium text-base`}
                            >
                              {dayNum}
                            </Text>
                          </View>
                        </View>
                      </TouchableOpacity>
                    </View>
                  );
                }

                // Create a week container with evenly distributed days
                allWeeks.push(
                  <View
                    key={`week-${weekOffset}`}
                    className="flex-row"
                    style={{ width: windowWidth }}
                  >
                    {daysOfWeek}
                  </View>
                );
              }

              // Important: Make sure weeks are ordered correctly - oldest first (left), newest last (right)
              return allWeeks;
            })()}
          </ScrollView>
        </View>
      </View>
    </Animated.View>
  );
}
