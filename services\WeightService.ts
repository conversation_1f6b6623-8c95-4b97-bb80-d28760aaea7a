import { supabase, getCurrentUserId } from "../config/supabase";
import { WeightEntry } from "../context/WeightContext";

interface SupabaseWeightEntry {
  id: string;
  user_id: string;
  date: string;
  weight: number;
  created_at: string;
}

// Convert from app format to database format
const transformWeightEntryForSupabase = async (
  entry: WeightEntry
): Promise<Omit<SupabaseWeightEntry, "id" | "created_at">> => {
  const userId = await getCurrentUserId();

  if (!userId) {
    throw new Error(
      "User ID not available. Please ensure you are authenticated."
    );
  }

  return {
    user_id: userId,
    date: entry.date,
    weight: entry.weight,
  };
};

// Convert from database format to app format
const transformWeightEntryFromSupabase = (
  entry: SupabaseWeightEntry
): WeightEntry => {
  return {
    date: entry.date,
    weight: entry.weight,
  };
};

// Get all weight history
export const getAllWeightEntries = async (): Promise<WeightEntry[]> => {
  try {
    const userId = await getCurrentUserId();

    if (!userId) {
      console.error(
        "User ID not available. Please ensure you are authenticated."
      );
      return [];
    }

    const { data, error } = await supabase
      .from("weight_history")
      .select("*")
      .eq("user_id", userId)
      .order("date", { ascending: false });

    if (error) {
      console.error("Error fetching weight history:", error);
      return [];
    }

    return data?.map(transformWeightEntryFromSupabase) || [];
  } catch (error) {
    console.error("Error in getAllWeightEntries:", error);
    return [];
  }
};

// Add a new weight entry
export const addWeightEntry = async (
  weight: number,
  isMetric: boolean
): Promise<WeightEntry | null> => {
  try {
    // Convert to kg if input is in lbs
    const weightInKg = isMetric ? weight : weight * 0.453592;

    const today = new Date().toISOString().split("T")[0];
    const entry: WeightEntry = { date: today, weight: weightInKg };

    const supabaseEntry = await transformWeightEntryForSupabase(entry);
    const userId = await getCurrentUserId();

    if (!userId) {
      console.error(
        "User ID not available. Please ensure you are authenticated."
      );
      return null;
    }

    // Check if we already have an entry for today
    const { data: existingEntry, error: queryError } = await supabase
      .from("weight_history")
      .select("id")
      .eq("user_id", userId)
      .eq("date", today)
      .maybeSingle();

    if (queryError) {
      console.error("Error checking if weight entry exists:", queryError);
      return null;
    }

    if (existingEntry) {
      // Update existing entry
      const { error } = await supabase
        .from("weight_history")
        .update({ weight: weightInKg })
        .eq("id", existingEntry.id)
        .eq("user_id", userId);

      if (error) {
        console.error("Error updating weight entry:", error);
        return null;
      }
    } else {
      // Insert new entry
      const { error } = await supabase
        .from("weight_history")
        .insert(supabaseEntry);

      if (error) {
        console.error("Error adding weight entry:", error);
        return null;
      }
    }

    return entry;
  } catch (error) {
    console.error("Error in addWeightEntry:", error);
    return null;
  }
};

// Update an existing weight entry
export const updateWeightEntry = async (
  date: string,
  weight: number,
  isMetric: boolean
): Promise<boolean> => {
  try {
    // Convert to kg if input is in lbs
    const weightInKg = isMetric ? weight : weight * 0.453592;

    const userId = await getCurrentUserId();

    if (!userId) {
      console.error(
        "User ID not available. Please ensure you are authenticated."
      );
      return false;
    }

    const { error } = await supabase
      .from("weight_history")
      .update({ weight: weightInKg })
      .eq("user_id", userId)
      .eq("date", date);

    if (error) {
      console.error("Error updating weight entry:", error);
      return false;
    }

    return true;
  } catch (error) {
    console.error("Error in updateWeightEntry:", error);
    return false;
  }
};

// Delete a weight entry
export const deleteWeightEntry = async (date: string): Promise<boolean> => {
  try {
    const userId = await getCurrentUserId();

    if (!userId) {
      console.error(
        "User ID not available. Please ensure you are authenticated."
      );
      return false;
    }

    const { error } = await supabase
      .from("weight_history")
      .delete()
      .eq("user_id", userId)
      .eq("date", date);

    if (error) {
      console.error("Error deleting weight entry:", error);
      return false;
    }

    return true;
  } catch (error) {
    console.error("Error in deleteWeightEntry:", error);
    return false;
  }
};

// Clear all weight history
export const clearWeightHistory = async (): Promise<boolean> => {
  try {
    const userId = await getCurrentUserId();

    if (!userId) {
      console.error(
        "User ID not available. Please ensure you are authenticated."
      );
      return false;
    }

    const { error } = await supabase
      .from("weight_history")
      .delete()
      .eq("user_id", userId);

    if (error) {
      console.error("Error clearing weight history:", error);
      return false;
    }

    return true;
  } catch (error) {
    console.error("Error in clearWeightHistory:", error);
    return false;
  }
};
