# Supabase Integration for Calorie-AI

This application has been integrated with Supabase as its backend database. This document explains the database structure, how to set up the database, and how the app interacts with the backend.

## Database Structure

The Supabase database consists of the following tables:

1. **profiles** - Stores user profile information

   - Linked to auth.users via user id
   - Contains weight, height, age, gender, activity level, goals, etc.

2. **food_logs** - Stores user food entries

   - Each entry contains nutritional info, date, meal type, etc.
   - Linked to users via user_id

3. **exercise_logs** - Stores user exercise activities

   - Contains exercise type, duration, calories burned, etc.
   - Linked to users via user_id

4. **weight_history** - Stores user weight tracking
   - Contains weight measurements with dates
   - Linked to users via user_id

## Row Level Security

All tables have Row Level Security (RLS) enabled, ensuring that users can only access their own data. The policies enforce:

- Users can only view their own data
- Users can only insert their own data
- Users can only update their own data
- Users can only delete their own data

## Device-Based Authentication

The app uses device-based authentication with anonymous accounts:

1. When a user first opens the app, a unique device ID is generated
2. An anonymous account is created in Supabase auth using this device ID
3. All data is associated with this anonymous account
4. Data is only accessible from the same device

## Setup Instructions

### 1. Create Supabase Project

If you haven't already:

1. Go to [Supabase](https://supabase.com) and create an account
2. Create a new project
3. Note your Project URL and anon key (found under Project Settings > API)

### 2. Run Migration Script

Execute the SQL in `scripts/supabase-migration.sql` using the Supabase SQL Editor. This will:

- Create the necessary tables
- Set up Row Level Security
- Create database policies
- Create necessary indexes

### 3. Update Environment Variables

Make sure to set the following environment variables:

```
EXPO_PUBLIC_SUPABASE_URL=your_project_url
EXPO_PUBLIC_SUPABASE_ANON_KEY=your_anon_key
```

## Data Migration

The app handles migrating local data to Supabase automatically:

1. On startup, it attempts to fetch data from Supabase
2. If no data exists in Supabase but local data exists, it migrates the local data to Supabase
3. After successful migration, local data is deleted

## Offline Support

The app maintains local state for all data, which means:

- Users can view their data even when offline
- Changes are immediately reflected in the UI
- Data is synced with Supabase when the device is online

## Error Handling

If Supabase operations fail:

- The app falls back to using local state
- Local storage may be used as a backup
- Errors are logged to the console

## Tables Structure

### profiles

```sql
CREATE TABLE profiles (
  id UUID REFERENCES auth.users(id) PRIMARY KEY,
  device_id TEXT NOT NULL,
  weight FLOAT,
  height FLOAT,
  age INTEGER,
  gender TEXT,
  activity_level TEXT,
  goal_type TEXT,
  target_weight FLOAT,
  weight_change_rate FLOAT,
  daily_calorie_goal INTEGER,
  protein INTEGER,
  carbs INTEGER,
  fat INTEGER,
  initialized BOOLEAN DEFAULT false,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
```

### food_logs

```sql
CREATE TABLE food_logs (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  user_id UUID REFERENCES auth.users(id) NOT NULL,
  name TEXT NOT NULL,
  calories FLOAT NOT NULL,
  protein FLOAT NOT NULL,
  carbs FLOAT NOT NULL,
  fat FLOAT NOT NULL,
  serving_size TEXT,
  image_url TEXT,
  date DATE NOT NULL,
  meal_type TEXT NOT NULL,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
```

### exercise_logs

```sql
CREATE TABLE exercise_logs (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  user_id UUID REFERENCES auth.users(id) NOT NULL,
  name TEXT NOT NULL,
  date DATE NOT NULL,
  duration FLOAT NOT NULL,
  calories_burned FLOAT NOT NULL,
  exercise_type TEXT NOT NULL,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
```

### weight_history

```sql
CREATE TABLE weight_history (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  user_id UUID REFERENCES auth.users(id) NOT NULL,
  date DATE NOT NULL,
  weight FLOAT NOT NULL,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  UNIQUE(user_id, date)
);
```
