import React, { useState, useEffect } from "react";
import {
  View,
  Text,
  ScrollView,
  TouchableOpacity,
  Alert,
  Animated,
  ActivityIndicator,
  TextInput,
  Switch,
} from "react-native";
import { useRouter } from "expo-router";
import { LinearGradient } from "expo-linear-gradient";
import { Ionicons } from "@expo/vector-icons";
import * as Haptics from "expo-haptics";

import {
  useUserProfile,
  ActivityLevel,
  GoalType,
} from "@/context/UserProfileContext";
import { useTheme } from "@/context/ThemeContext";

export default function ProfileScreen() {
  const router = useRouter();
  const { resolvedTheme } = useTheme();
  const {
    profile,
    updateProfile,
    saveProfile,
    calculateBMR,
    calculateTDEE,
    calculateCalorieGoal,
    isProfileComplete,
  } = useUserProfile();

  const [isMetric, setIsMetric] = useState(true);
  const [weight, setWeight] = useState(profile.weight.toString());
  const [height, setHeight] = useState(profile.height.toString());
  const [age, setAge] = useState(profile.age.toString());
  const [gender, setGender] = useState<"male" | "female" | "other">(
    profile.gender
  );
  const [activityLevel, setActivityLevel] = useState<ActivityLevel>(
    profile.activityLevel
  );
  const [goalType, setGoalType] = useState<GoalType>(profile.goalType);
  const [targetWeight, setTargetWeight] = useState(
    profile.targetWeight ? profile.targetWeight.toString() : ""
  );
  const [weightChangeRate, setWeightChangeRate] = useState(
    profile.weightChangeRate ? profile.weightChangeRate.toString() : "1.0"
  );

  const [isLoading, setIsLoading] = useState(false);
  const [currentStep, setCurrentStep] = useState(1);
  const [showAdvanced, setShowAdvanced] = useState(false);

  // Animation values
  const fadeAnim = React.useRef(new Animated.Value(0)).current;
  const slideAnim = React.useRef(new Animated.Value(20)).current;

  // Create refs for the ScrollViews
  const heightScrollViewRef = React.useRef<ScrollView>(null);
  const weightScrollViewRef = React.useRef<ScrollView>(null);
  const feetScrollViewRef = React.useRef<ScrollView>(null);
  const inchesScrollViewRef = React.useRef<ScrollView>(null);
  const poundsScrollViewRef = React.useRef<ScrollView>(null);

  // Function to calculate scroll position for a value
  const calculateScrollPosition = (
    value: number,
    startValue: number,
    itemHeight: number = 56
  ) => {
    return (value - startValue) * itemHeight;
  };

  // Create a helper function to scroll to a specific value
  const scrollToValue = (
    scrollViewRef: React.RefObject<ScrollView>,
    currentValue: number,
    minValue: number,
    itemHeight: number = 56
  ) => {
    if (scrollViewRef.current) {
      const position = (currentValue - minValue) * itemHeight;
      scrollViewRef.current.scrollTo({ y: position, animated: false });
    }
  };

  useEffect(() => {
    animateContent(1);
  }, []);

  useEffect(() => {
    // Initialize with stored profile values if they exist
    if (profile.height) {
      if (isMetric) {
        // Display in metric (cm)
        setHeight(Math.round(profile.height).toString());
        setWeight(Math.round(profile.weight).toString());
      } else {
        // Convert to imperial and display (ft/in and lb)
        // Convert cm to total inches, then split into feet and inches
        const totalInches = profile.height / 2.54;
        const feet = Math.floor(totalInches / 12);
        const inches = Math.round(totalInches % 12);
        setHeight((feet * 12 + inches).toString());

        // Convert kg to lb
        setWeight(Math.round(profile.weight * 2.20462).toString());
      }
    }
  }, [isMetric, profile.height, profile.weight]);

  useEffect(() => {
    if (currentStep !== 2) return; // Only run if we're on the height/weight step

    // Small timeout to ensure the ScrollViews are rendered
    setTimeout(() => {
      if (isMetric) {
        // Scroll metric ScrollViews to current values
        const heightValue = parseInt(height);
        const weightValue = parseInt(weight);

        scrollToValue(heightScrollViewRef, heightValue, 130);
        scrollToValue(weightScrollViewRef, weightValue, 30);
      } else {
        // Scroll imperial ScrollViews to current values
        const totalInches = parseInt(height);
        const feet = Math.floor(totalInches / 12);
        const inches = totalInches % 12;
        const pounds = parseInt(weight);

        scrollToValue(feetScrollViewRef, feet, 0);
        scrollToValue(inchesScrollViewRef, inches, 0);
        scrollToValue(poundsScrollViewRef, pounds, 50);
      }
    }, 100);
  }, [currentStep, isMetric, height, weight]);

  const animateContent = (toValue: number) => {
    Animated.parallel([
      Animated.spring(fadeAnim, {
        toValue,
        tension: 50,
        friction: 7,
        useNativeDriver: true,
      }),
      Animated.spring(slideAnim, {
        toValue: toValue === 1 ? 0 : 20,
        tension: 50,
        friction: 7,
        useNativeDriver: true,
      }),
    ]).start();
  };

  const handleSaveProfile = async () => {
    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);

    // Validate inputs
    if (!weight || !height || !age) {
      Alert.alert("Missing Information", "Please fill in all required fields");
      return;
    }

    // Convert units if needed
    let weightKg = parseFloat(weight);
    let heightCm = parseFloat(height);

    if (!isMetric) {
      // Convert lbs to kg
      weightKg = weightKg * 0.453592;
      // Height is already stored as total inches when in imperial
      // Convert inches to cm
      heightCm = heightCm * 2.54;
    }

    // Convert target weight if provided
    let targetWeightKg;
    if (targetWeight) {
      targetWeightKg = parseFloat(targetWeight);
      if (!isMetric) {
        targetWeightKg = targetWeightKg * 0.453592;
      }
    }

    try {
      setIsLoading(true);
      animateContent(0);

      // Short delay to ensure animation completes
      await new Promise((resolve) => setTimeout(resolve, 300));

      // Update profile data
      updateProfile({
        weight: weightKg,
        height: heightCm,
        age: parseInt(age),
        gender,
        activityLevel,
        goalType,
        targetWeight: targetWeightKg,
        weightChangeRate: parseFloat(weightChangeRate),
      });

      // Save profile to storage
      await saveProfile();

      // Navigate to home screen
      router.replace("/(tabs)");
    } catch (error) {
      console.error("Error saving profile:", error);
      Alert.alert("Error", "Failed to save profile. Please try again.");
    } finally {
      setIsLoading(false);
    }
  };

  // Add handler for unit system toggle to convert values
  const handleUnitSystemToggle = (useMetric: boolean) => {
    if (isMetric === useMetric) return;

    let newHeight, newWeight;

    if (useMetric) {
      // Converting from imperial to metric
      const totalInches = parseInt(height);
      newHeight = Math.round(totalInches * 2.54);

      const pounds = parseInt(weight);
      newWeight = Math.round(pounds / 2.20462);
    } else {
      // Converting from metric to imperial
      const cm = parseInt(height);
      newHeight = Math.round(cm / 2.54);

      const kg = parseInt(weight);
      newWeight = Math.round(kg * 2.20462);
    }

    // Update state
    setHeight(newHeight.toString());
    setWeight(newWeight.toString());
    setIsMetric(useMetric);
  };

  // Reusable Input Component
  const StyledInput = ({ label, unit, ...props }: any) => (
    <View className="space-y-2">
      <Text className="text-gray-300">
        {label} {unit}
      </Text>
      <TextInput
        className="bg-white/10 text-white px-4 py-3 rounded-xl border border-white/10 focus:border-green-500"
        placeholderTextColor="#888"
        keyboardType="numeric"
        {...props}
      />
    </View>
  );

  // Reusable Selection Button Component
  const SelectionButton = ({ text, onPress, isSelected }: any) => (
    <TouchableOpacity
      className={`flex-1 py-3 px-4 rounded-xl border ${
        isSelected
          ? "bg-gray-100/60 border-gray-400"
          : "bg-gray-100/60 border-transparent"
      }`}
      onPress={() => {
        Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
        onPress();
      }}
    >
      <Text
        className={`text-center ${
          isSelected ? "text-gray-800 font-semibold" : "text-gray-300"
        }`}
      >
        {text}
      </Text>
    </TouchableOpacity>
  );

  // Reusable Large Selection Button Component
  const LargeSelectionButton = ({
    title,
    description,
    onPress,
    isSelected,
  }: any) => (
    <TouchableOpacity
      className={`p-4 rounded-xl border ${
        isSelected
          ? "bg-gray-100/60 border-gray-400"
          : "bg-gray-100/60 border-transparent"
      } mb-3`}
      onPress={() => {
        Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
        onPress();
      }}
    >
      <Text
        className={`font-semibold ${
          isSelected ? "text-gray-800" : "text-gray-400"
        }`}
      >
        {title}
      </Text>
      <Text className={`${isSelected ? "text-gray-200" : "text-gray-400"}`}>
        {description}
      </Text>
    </TouchableOpacity>
  );

  const renderStep1 = () => (
    <Animated.View
      style={{
        opacity: fadeAnim,
        transform: [{ translateY: slideAnim }],
      }}
      className="w-full"
    >
      <View
        className={`rounded-3xl p-6 mb-6 backdrop-blur-xl border ${
          resolvedTheme === "light"
            ? "bg-white border-gray-200/60"
            : "bg-gray-800 border-gray-700"
        }`}
      >
        <Text
          className={`text-3xl font-bold mb-2 ${
            resolvedTheme === "light" ? "text-gray-800" : "text-white"
          }`}
        >
          Basic Information
        </Text>
        <Text
          className={`text-lg mb-8 ${
            resolvedTheme === "light" ? "text-gray-500" : "text-gray-400"
          }`}
        >
          Let's personalize your experience
        </Text>

        <View className="mb-8">
          <Text
            className={`text-xl mb-3 font-medium ${
              resolvedTheme === "light" ? "text-gray-700" : "text-gray-300"
            }`}
          >
            Age
          </Text>
          <View
            className={`rounded-xl p-1 h-14 ${
              resolvedTheme === "light"
                ? "bg-gray-100/60 border border-gray-200/60"
                : "bg-black/20 border border-gray-700"
            }`}
          >
            <TextInput
              className={`bg-transparent text-xl px-4 py-2 h-full ${
                resolvedTheme === "light" ? "text-gray-800" : "text-white"
              }`}
              placeholderTextColor={
                resolvedTheme === "light" ? "#6b7280" : "#777"
              }
              value={age}
              onChangeText={setAge}
              placeholder="e.g. 30"
              keyboardType="numeric"
            />
          </View>
        </View>

        <View className="space-y-3">
          <Text
            className={`text-xl mb-3 font-medium ${
              resolvedTheme === "light" ? "text-gray-700" : "text-gray-300"
            }`}
          >
            Gender
          </Text>
          <View className="flex-row">
            {["male", "female", "other"].map((option, index, array) => (
              <TouchableOpacity
                key={option}
                onPress={() => setGender(option as "male" | "female" | "other")}
                style={{
                  flex: 1,
                  marginRight: index < array.length - 1 ? 4 : 0,
                }}
                className={`py-4 rounded-xl flex-row justify-center items-center border ${
                  resolvedTheme === "light"
                    ? "bg-gray-100/60 border border-gray-200/60"
                    : "bg-black/20 border border-gray-700"
                } ${
                  gender === option
                    ? resolvedTheme === "light"
                      ? "border-2 border-gray-400"
                      : "border-2 border-gray-600"
                    : ""
                }`}
              >
                <Ionicons
                  name={
                    option === "male"
                      ? "male"
                      : option === "female"
                      ? "female"
                      : "person"
                  }
                  size={18}
                  color={
                    gender === option
                      ? resolvedTheme === "light"
                        ? "#374151"
                        : "#FFFFFF"
                      : resolvedTheme === "light"
                      ? "#6b7280"
                      : "#999"
                  }
                  style={{ marginRight: 6 }}
                />
                <Text
                  className={`${
                    gender === option
                      ? resolvedTheme === "light"
                        ? "text-gray-800 font-semibold"
                        : "text-white font-semibold"
                      : resolvedTheme === "light"
                      ? "text-gray-500"
                      : "text-gray-400"
                  } capitalize`}
                >
                  {option}
                </Text>
              </TouchableOpacity>
            ))}
          </View>
        </View>
      </View>
    </Animated.View>
  );

  const renderStep2 = () => (
    <Animated.View
      style={{
        opacity: fadeAnim,
        transform: [{ translateY: slideAnim }],
      }}
      className="w-full"
    >
      <View
        className={`rounded-3xl p-6 mb-6 backdrop-blur-xl border ${
          resolvedTheme === "light"
            ? "bg-white border-gray-200/60"
            : "bg-gray-800 border-gray-700"
        }`}
      >
        <Text
          className={`text-3xl font-bold mb-2 ${
            resolvedTheme === "light" ? "text-gray-800" : "text-white"
          }`}
        >
          Body Measurements
        </Text>
        <Text
          className={`text-lg mb-8 ${
            resolvedTheme === "light" ? "text-gray-500" : "text-gray-400"
          }`}
        >
          This helps us calculate your daily goals
        </Text>

        <View className="mb-6">
          <View className="flex-row items-center justify-between mb-4">
            <Text
              className={`text-xl font-medium ${
                resolvedTheme === "light" ? "text-gray-700" : "text-gray-300"
              }`}
            >
              Unit System
            </Text>
            <View
              className={`flex-row rounded-full p-1 ${
                resolvedTheme === "light" ? "bg-gray-100/60" : "bg-black/20"
              }`}
            >
              <TouchableOpacity
                onPress={() => handleUnitSystemToggle(true)}
                className={`px-5 py-2 rounded-full ${
                  isMetric
                    ? resolvedTheme === "light"
                      ? "bg-gray-800"
                      : "bg-white/50"
                    : "bg-transparent"
                }`}
              >
                <Text
                  className={
                    isMetric
                      ? resolvedTheme === "light"
                        ? "text-white font-semibold"
                        : "text-black font-semibold"
                      : resolvedTheme === "light"
                      ? "text-gray-500"
                      : "text-gray-400"
                  }
                >
                  Metric
                </Text>
              </TouchableOpacity>
              <TouchableOpacity
                onPress={() => handleUnitSystemToggle(false)}
                className={`px-5 py-2 rounded-full ${
                  !isMetric
                    ? resolvedTheme === "light"
                      ? "bg-gray-800"
                      : "bg-white/50"
                    : "bg-transparent"
                }`}
              >
                <Text
                  className={
                    !isMetric
                      ? resolvedTheme === "light"
                        ? "text-white font-semibold"
                        : "text-black font-semibold"
                      : resolvedTheme === "light"
                      ? "text-gray-500"
                      : "text-gray-400"
                  }
                >
                  Imperial
                </Text>
              </TouchableOpacity>
            </View>
          </View>
        </View>

        {isMetric ? (
          // Metric System UI
          <View className="space-y-8">
            <View className="flex-row justify-between">
              <View className="flex-1 mr-4">
                <Text
                  className={`text-2xl font-semibold mb-4 ${
                    resolvedTheme === "light" ? "text-gray-800" : "text-white"
                  }`}
                >
                  Height
                </Text>
                <Text
                  className={
                    resolvedTheme === "light"
                      ? "text-gray-600"
                      : "text-gray-300"
                  }
                >
                  cm
                </Text>
                <View
                  className={`rounded-xl relative h-[140px] overflow-hidden ${
                    resolvedTheme === "light" ? "bg-gray-100/60" : "bg-black/20"
                  }`}
                >
                  {/* Fixed highlight for selected value */}
                  <View
                    className={`absolute left-0 right-0 h-[56px] bg-transparent border rounded-xl z-10 pointer-events-none ${
                      resolvedTheme === "light"
                        ? "border-gray-400"
                        : "border-gray-600"
                    }`}
                    style={{ top: 42 }}
                  />

                  <ScrollView
                    ref={heightScrollViewRef}
                    showsVerticalScrollIndicator={false}
                    contentContainerStyle={{ paddingVertical: 42 }}
                    snapToInterval={56}
                    decelerationRate="fast"
                    className="h-[140px]"
                    onMomentumScrollEnd={(e) => {
                      const offsetY = e.nativeEvent.contentOffset.y;
                      const selectedIndex = Math.round(offsetY / 56);
                      const selectedValue = 130 + selectedIndex;
                      setHeight(selectedValue.toString());
                    }}
                  >
                    {Array.from({ length: 91 }, (_, i) => i + 130).map((cm) => (
                      <TouchableOpacity
                        key={`height-${cm}`}
                        onPress={() => setHeight(cm.toString())}
                        className="h-[56px] px-4 items-center justify-center"
                      >
                        <Text
                          className={`text-xl font-medium ${
                            parseInt(height) === cm
                              ? resolvedTheme === "light"
                                ? "text-gray-800"
                                : "text-white"
                              : resolvedTheme === "light"
                              ? "text-gray-500"
                              : "text-gray-400"
                          }`}
                        >
                          {cm}
                        </Text>
                      </TouchableOpacity>
                    ))}
                  </ScrollView>
                </View>
              </View>

              <View className="flex-1 ml-4">
                <Text
                  className={`text-2xl font-semibold mb-4 ${
                    resolvedTheme === "light" ? "text-gray-800" : "text-white"
                  }`}
                >
                  Weight
                </Text>
                <Text
                  className={
                    resolvedTheme === "light"
                      ? "text-gray-600"
                      : "text-gray-300"
                  }
                >
                  kg
                </Text>
                <View
                  className={`rounded-xl relative h-[140px] overflow-hidden ${
                    resolvedTheme === "light" ? "bg-gray-100/60" : "bg-black/20"
                  }`}
                >
                  {/* Fixed highlight for selected value */}
                  <View
                    className={`absolute left-0 right-0 h-[56px] bg-transparent border rounded-xl z-10 pointer-events-none ${
                      resolvedTheme === "light"
                        ? "border-gray-400"
                        : "border-gray-600"
                    }`}
                    style={{ top: 42 }}
                  />

                  <ScrollView
                    ref={weightScrollViewRef}
                    showsVerticalScrollIndicator={false}
                    contentContainerStyle={{ paddingVertical: 42 }}
                    snapToInterval={56}
                    decelerationRate="fast"
                    className="h-[140px]"
                    onMomentumScrollEnd={(e) => {
                      const offsetY = e.nativeEvent.contentOffset.y;
                      const selectedIndex = Math.round(offsetY / 56);
                      const selectedValue = 30 + selectedIndex;
                      setWeight(selectedValue.toString());
                    }}
                  >
                    {Array.from({ length: 151 }, (_, i) => i + 30).map((kg) => (
                      <TouchableOpacity
                        key={`weight-${kg}`}
                        onPress={() => setWeight(kg.toString())}
                        className="h-[56px] px-4 items-center justify-center"
                      >
                        <Text
                          className={`text-xl font-medium ${
                            parseInt(weight) === kg
                              ? resolvedTheme === "light"
                                ? "text-gray-800"
                                : "text-white"
                              : resolvedTheme === "light"
                              ? "text-gray-500"
                              : "text-gray-400"
                          }`}
                        >
                          {kg}
                        </Text>
                      </TouchableOpacity>
                    ))}
                  </ScrollView>
                </View>
              </View>
            </View>
          </View>
        ) : (
          // Imperial System UI
          <View className="space-y-8">
            <View className="flex-row justify-between">
              <View className="flex-1 mr-4">
                <Text
                  className={`text-2xl font-semibold mb-4 ${
                    resolvedTheme === "light" ? "text-gray-800" : "text-white"
                  }`}
                >
                  Height
                </Text>
                <View className="flex-row">
                  <View className="flex-1" style={{ marginRight: 4 }}>
                    <Text
                      className={`text-center mb-2 ${
                        resolvedTheme === "light"
                          ? "text-gray-600"
                          : "text-gray-300"
                      }`}
                    >
                      ft
                    </Text>
                    <View
                      className={`rounded-xl relative h-[140px] overflow-hidden ${
                        resolvedTheme === "light"
                          ? "bg-gray-100/60"
                          : "bg-black/20"
                      }`}
                    >
                      {/* Fixed highlight for selected value */}
                      <View
                        className={`absolute left-0 right-0 h-[56px] bg-transparent border rounded-xl z-10 pointer-events-none ${
                          resolvedTheme === "light"
                            ? "border-gray-400"
                            : "border-gray-600"
                        }`}
                        style={{ top: 42 }}
                      />

                      <ScrollView
                        ref={feetScrollViewRef}
                        showsVerticalScrollIndicator={false}
                        contentContainerStyle={{ paddingVertical: 42 }}
                        snapToInterval={56}
                        decelerationRate="fast"
                        className="h-[140px]"
                        onMomentumScrollEnd={(e) => {
                          const offsetY = e.nativeEvent.contentOffset.y;
                          const selectedIndex = Math.round(offsetY / 56);
                          const inches = parseInt(height) % 12;
                          setHeight((selectedIndex * 12 + inches).toString());
                        }}
                      >
                        {Array.from({ length: 8 }, (_, i) => i).map((ft) => (
                          <TouchableOpacity
                            key={`feet-${ft}`}
                            onPress={() => {
                              const inches = parseInt(height) % 12;
                              setHeight((ft * 12 + inches).toString());
                            }}
                            className="h-[56px] px-4 items-center justify-center"
                          >
                            <Text
                              className={`text-xl text-center font-medium ${
                                Math.floor(parseInt(height) / 12) === ft
                                  ? resolvedTheme === "light"
                                    ? "text-gray-800"
                                    : "text-white"
                                  : resolvedTheme === "light"
                                  ? "text-gray-500"
                                  : "text-gray-400"
                              }`}
                            >
                              {ft}
                            </Text>
                          </TouchableOpacity>
                        ))}
                      </ScrollView>
                    </View>
                  </View>

                  <View className="flex-1">
                    <Text
                      className={`text-center mb-2 ${
                        resolvedTheme === "light"
                          ? "text-gray-600"
                          : "text-gray-300"
                      }`}
                    >
                      in
                    </Text>
                    <View
                      className={`rounded-xl relative h-[140px] overflow-hidden ${
                        resolvedTheme === "light"
                          ? "bg-gray-100/60"
                          : "bg-black/20"
                      }`}
                    >
                      {/* Fixed highlight for selected value */}
                      <View
                        className={`absolute left-0 right-0 h-[56px] bg-transparent border rounded-xl z-10 pointer-events-none ${
                          resolvedTheme === "light"
                            ? "border-gray-400"
                            : "border-gray-600"
                        }`}
                        style={{ top: 42 }}
                      />

                      <ScrollView
                        ref={inchesScrollViewRef}
                        showsVerticalScrollIndicator={false}
                        contentContainerStyle={{ paddingVertical: 42 }}
                        snapToInterval={56}
                        decelerationRate="fast"
                        className="h-[140px]"
                        onMomentumScrollEnd={(e) => {
                          const offsetY = e.nativeEvent.contentOffset.y;
                          const selectedIndex = Math.round(offsetY / 56);
                          const feet = Math.floor(parseInt(height) / 12);
                          setHeight((feet * 12 + selectedIndex).toString());
                        }}
                      >
                        {Array.from({ length: 12 }, (_, i) => i).map((inch) => (
                          <TouchableOpacity
                            key={`inch-${inch}`}
                            onPress={() => {
                              const feet = Math.floor(parseInt(height) / 12);
                              setHeight((feet * 12 + inch).toString());
                            }}
                            className="h-[56px] px-4 items-center justify-center"
                          >
                            <Text
                              className={`text-xl text-center font-medium ${
                                parseInt(height) % 12 === inch
                                  ? resolvedTheme === "light"
                                    ? "text-gray-800"
                                    : "text-white"
                                  : resolvedTheme === "light"
                                  ? "text-gray-500"
                                  : "text-gray-400"
                              }`}
                            >
                              {inch}
                            </Text>
                          </TouchableOpacity>
                        ))}
                      </ScrollView>
                    </View>
                  </View>
                </View>
              </View>

              <View className="flex-1 ml-4">
                <Text
                  className={`text-2xl font-semibold mb-4 ${
                    resolvedTheme === "light" ? "text-gray-800" : "text-white"
                  }`}
                >
                  Weight
                </Text>
                <Text
                  className={
                    resolvedTheme === "light"
                      ? "text-gray-600"
                      : "text-gray-300"
                  }
                >
                  lb
                </Text>
                <View
                  className={`rounded-xl relative h-[140px] overflow-hidden ${
                    resolvedTheme === "light" ? "bg-gray-100/60" : "bg-black/20"
                  }`}
                >
                  {/* Fixed highlight for selected value */}
                  <View
                    className={`absolute left-0 right-0 h-[56px] bg-transparent border rounded-xl z-10 pointer-events-none ${
                      resolvedTheme === "light"
                        ? "border-gray-400"
                        : "border-gray-600"
                    }`}
                    style={{ top: 42 }}
                  />

                  <ScrollView
                    ref={poundsScrollViewRef}
                    showsVerticalScrollIndicator={false}
                    contentContainerStyle={{ paddingVertical: 42 }}
                    snapToInterval={56}
                    decelerationRate="fast"
                    className="h-[140px]"
                    onMomentumScrollEnd={(e) => {
                      const offsetY = e.nativeEvent.contentOffset.y;
                      const selectedIndex = Math.round(offsetY / 56);
                      const selectedValue = 50 + selectedIndex;
                      setWeight(selectedValue.toString());
                    }}
                  >
                    {Array.from({ length: 251 }, (_, i) => i + 50).map((lb) => (
                      <TouchableOpacity
                        key={`weight-${lb}`}
                        onPress={() => setWeight(lb.toString())}
                        className="h-[56px] px-4 items-center justify-center"
                      >
                        <Text
                          className={`text-xl text-center font-medium ${
                            parseInt(weight) === lb
                              ? resolvedTheme === "light"
                                ? "text-gray-800"
                                : "text-white"
                              : resolvedTheme === "light"
                              ? "text-gray-500"
                              : "text-gray-400"
                          }`}
                        >
                          {lb}
                        </Text>
                      </TouchableOpacity>
                    ))}
                  </ScrollView>
                </View>
              </View>
            </View>
          </View>
        )}
      </View>
    </Animated.View>
  );

  const renderStep3 = () => (
    <Animated.View
      style={{
        opacity: fadeAnim,
        transform: [{ translateY: slideAnim }],
      }}
      className="w-full"
    >
      <View
        className={`rounded-3xl p-6 mb-6 backdrop-blur-xl border ${
          resolvedTheme === "light"
            ? "bg-white border-gray-200/60"
            : "bg-gray-800 border-gray-700"
        }`}
      >
        <Text
          className={`text-3xl font-bold mb-2 ${
            resolvedTheme === "light" ? "text-gray-800" : "text-white"
          }`}
        >
          Activity Level
        </Text>
        <Text
          className={`text-lg mb-8 ${
            resolvedTheme === "light" ? "text-gray-500" : "text-gray-400"
          }`}
        >
          How active are you on a typical day?
        </Text>

        <View className="space-y-4">
          {[
            {
              level: "sedentary",
              title: "Sedentary",
              description: "Little or no exercise, desk job",
              icon: "body-outline",
            },
            {
              level: "lightlyActive",
              title: "Lightly Active",
              description: "Light exercise 1-3 days/week",
              icon: "walk-outline",
            },
            {
              level: "moderatelyActive",
              title: "Moderately Active",
              description: "Moderate exercise 3-5 days/week",
              icon: "bicycle-outline",
            },
            {
              level: "veryActive",
              title: "Very Active",
              description: "Hard exercise 6-7 days/week",
              icon: "fitness-outline",
            },
            {
              level: "extraActive",
              title: "Extra Active",
              description: "Very hard exercise & physical job",
              icon: "barbell-outline",
            },
          ].map((item, index, array) => (
            <TouchableOpacity
              key={item.level}
              onPress={() => setActivityLevel(item.level as ActivityLevel)}
              style={{ marginBottom: index < array.length - 1 ? 3 : 0 }}
              className={`p-4 rounded-xl border ${
                resolvedTheme === "light"
                  ? "bg-gray-100/60 border border-gray-200/60"
                  : "bg-black/20 border border-gray-700"
              } ${
                activityLevel === item.level
                  ? resolvedTheme === "light"
                    ? "border-2 border-gray-400"
                    : "border-2 border-gray-600"
                  : ""
              }`}
            >
              <View className="flex-row items-center">
                <View
                  className={`w-10 h-10 rounded-full items-center justify-center mr-3 ${
                    activityLevel === item.level
                      ? "bg-transparent"
                      : resolvedTheme === "light"
                      ? "bg-gray-200/60"
                      : "bg-white/5"
                  }`}
                >
                  <Ionicons
                    name={item.icon as any}
                    size={20}
                    color={
                      activityLevel === item.level
                        ? resolvedTheme === "light"
                          ? "#374151"
                          : "#FFFFFF"
                        : resolvedTheme === "light"
                        ? "#6b7280"
                        : "#999"
                    }
                  />
                </View>
                <View className="flex-1">
                  <Text
                    className={`font-semibold text-lg ${
                      activityLevel === item.level
                        ? resolvedTheme === "light"
                          ? "text-gray-800"
                          : "text-white"
                        : resolvedTheme === "light"
                        ? "text-gray-600"
                        : "text-gray-300"
                    }`}
                  >
                    {item.title}
                  </Text>
                  <Text
                    className={
                      activityLevel === item.level
                        ? resolvedTheme === "light"
                          ? "text-gray-600"
                          : "text-gray-200"
                        : resolvedTheme === "light"
                        ? "text-gray-500"
                        : "text-gray-400"
                    }
                  >
                    {item.description}
                  </Text>
                </View>
              </View>
            </TouchableOpacity>
          ))}
        </View>
      </View>
    </Animated.View>
  );

  const renderStep4 = () => (
    <Animated.View
      style={{
        opacity: fadeAnim,
        transform: [{ translateY: slideAnim }],
      }}
      className="w-full"
    >
      <View
        className={`rounded-3xl p-6 mb-6 backdrop-blur-xl border ${
          resolvedTheme === "light"
            ? "bg-white border-gray-200/60"
            : "bg-gray-800 border-gray-700"
        }`}
      >
        <Text
          className={`text-3xl font-bold mb-2 ${
            resolvedTheme === "light" ? "text-gray-800" : "text-white"
          }`}
        >
          Your Goals
        </Text>
        <Text
          className={`text-lg mb-8 ${
            resolvedTheme === "light" ? "text-gray-500" : "text-gray-400"
          }`}
        >
          What would you like to achieve?
        </Text>

        <View className="mb-8">
          <View className="flex-row justify-between mb-5">
            {[
              { type: "lose", title: "Lose Weight", icon: "trending-down" },
              { type: "maintain", title: "Maintain", icon: "remove" },
              { type: "gain", title: "Gain Weight", icon: "trending-up" },
            ].map((goal, index, array) => (
              <TouchableOpacity
                key={goal.type}
                onPress={() => setGoalType(goal.type as GoalType)}
                style={{ marginRight: index < array.length - 1 ? 4 : 0 }}
                className={`flex-1 p-4 rounded-xl flex items-center ${
                  resolvedTheme === "light"
                    ? "bg-gray-100/60 border border-gray-200/60"
                    : "bg-black/20 border border-gray-700"
                } ${
                  goalType === goal.type
                    ? resolvedTheme === "light"
                      ? "border-2 border-gray-400"
                      : "border-2 border-gray-600"
                    : ""
                }`}
              >
                <View
                  className={`w-12 h-12 rounded-full items-center justify-center mb-2 ${
                    goalType === goal.type
                      ? "bg-transparent"
                      : resolvedTheme === "light"
                      ? "bg-gray-200/60"
                      : "bg-white/5"
                  }`}
                >
                  <Ionicons
                    name={goal.icon as any}
                    size={22}
                    color={
                      goalType === goal.type
                        ? resolvedTheme === "light"
                          ? "#374151"
                          : "#FFFFFF"
                        : resolvedTheme === "light"
                        ? "#6b7280"
                        : "#999"
                    }
                  />
                </View>
                <Text
                  className={`text-center ${
                    goalType === goal.type
                      ? resolvedTheme === "light"
                        ? "text-gray-800 font-semibold"
                        : "text-white font-semibold"
                      : resolvedTheme === "light"
                      ? "text-gray-500"
                      : "text-gray-400"
                  }`}
                >
                  {goal.title}
                </Text>
              </TouchableOpacity>
            ))}
          </View>
        </View>

        {goalType !== "maintain" && (
          <View className="space-y-6 mb-8">
            <View>
              <Text
                className={`text-xl mb-3 font-medium ${
                  resolvedTheme === "light" ? "text-gray-700" : "text-gray-300"
                }`}
              >
                Target Weight {isMetric ? "(kg)" : "(lbs)"}
              </Text>
              <View
                className={`rounded-xl p-1 h-14 ${
                  resolvedTheme === "light" ? "bg-gray-100/60" : "bg-black/20"
                }`}
              >
                <TextInput
                  className={`bg-transparent text-xl px-4 py-2 h-full ${
                    resolvedTheme === "light" ? "text-gray-800" : "text-white"
                  }`}
                  value={targetWeight}
                  onChangeText={setTargetWeight}
                  placeholder={`Target weight in ${isMetric ? "kg" : "lbs"}`}
                  placeholderTextColor={
                    resolvedTheme === "light" ? "#6b7280" : "#777"
                  }
                  keyboardType="numeric"
                />
              </View>
            </View>

            <View>
              <Text
                className={`text-xl mb-3 font-medium ${
                  resolvedTheme === "light" ? "text-gray-700" : "text-gray-300"
                }`}
              >
                {goalType === "lose" ? "Weight Loss" : "Weight Gain"} Rate
              </Text>
              <View
                className={`flex-row rounded-xl p-1.5 ${
                  resolvedTheme === "light" ? "bg-gray-100/60" : "bg-black/20"
                }`}
              >
                {["0.5", "1.0", "1.5", "2.0"].map((rate) => (
                  <TouchableOpacity
                    key={rate}
                    onPress={() => setWeightChangeRate(rate)}
                    className={`py-3 rounded-lg ${
                      weightChangeRate === rate
                        ? resolvedTheme === "light"
                          ? "bg-transparent border border-gray-400"
                          : "bg-transparent border border-gray-600"
                        : "bg-transparent"
                    }`}
                  >
                    <Text
                      className={`text-center font-medium ${
                        weightChangeRate === rate
                          ? resolvedTheme === "light"
                            ? "text-gray-800"
                            : "text-white"
                          : resolvedTheme === "light"
                          ? "text-gray-500"
                          : "text-gray-400"
                      }`}
                    >
                      {rate}
                    </Text>
                    <Text
                      className={`text-xs text-center ${
                        resolvedTheme === "light"
                          ? "text-gray-500"
                          : "text-gray-500"
                      }`}
                    >
                      lbs/week
                    </Text>
                  </TouchableOpacity>
                ))}
              </View>
            </View>
          </View>
        )}

        <TouchableOpacity
          onPress={() => setShowAdvanced(!showAdvanced)}
          className={`flex-row items-center justify-center py-4 rounded-xl mb-6 ${
            resolvedTheme === "light"
              ? "bg-gray-100/60 border border-gray-200/60"
              : "bg-black/20 border border-gray-700"
          }`}
        >
          <Text
            className={`mr-2 font-medium ${
              resolvedTheme === "light" ? "text-gray-700" : "text-gray-300"
            }`}
          >
            {showAdvanced ? "Hide Advanced Stats" : "Show Advanced Stats"}
          </Text>
          <Ionicons
            name={showAdvanced ? "chevron-up" : "chevron-down"}
            size={20}
            color={resolvedTheme === "light" ? "#6b7280" : "#999"}
          />
        </TouchableOpacity>

        {showAdvanced && (
          <View
            className={`rounded-xl p-5 space-y-4 border ${
              resolvedTheme === "light"
                ? "bg-gray-100/60 border-gray-200/60"
                : "bg-black/20 border-white/5"
            }`}
          >
            <View className="flex-row justify-between items-center">
              <Text
                className={
                  resolvedTheme === "light" ? "text-gray-600" : "text-gray-300"
                }
              >
                Basal Metabolic Rate
              </Text>
              <Text
                className={`font-semibold text-lg ${
                  resolvedTheme === "light" ? "text-gray-800" : "text-white"
                }`}
              >
                {Math.round(calculateBMR())} kcal
              </Text>
            </View>
            <View
              className={`h-px ${
                resolvedTheme === "light" ? "bg-gray-200" : "bg-white/10"
              }`}
            />
            <View className="flex-row justify-between items-center">
              <Text
                className={
                  resolvedTheme === "light" ? "text-gray-600" : "text-gray-300"
                }
              >
                Daily Energy Expenditure
              </Text>
              <Text
                className={`font-semibold text-lg ${
                  resolvedTheme === "light" ? "text-gray-800" : "text-white"
                }`}
              >
                {Math.round(calculateTDEE())} kcal
              </Text>
            </View>
            <View
              className={`h-px ${
                resolvedTheme === "light" ? "bg-gray-200" : "bg-white/10"
              }`}
            />
            <View className="flex-row justify-between items-center">
              <Text
                className={
                  resolvedTheme === "light" ? "text-gray-600" : "text-gray-300"
                }
              >
                Daily Calorie Goal
              </Text>
              <Text
                className={`font-semibold text-lg ${
                  resolvedTheme === "light" ? "text-gray-800" : "text-white"
                }`}
              >
                {Math.round(calculateCalorieGoal())} kcal
              </Text>
            </View>
          </View>
        )}
      </View>
    </Animated.View>
  );

  const handleNext = () => {
    // Save current step data
    if (currentStep < 4) {
      setCurrentStep(currentStep + 1);
      animateContent(1);
    } else {
      handleSaveProfile();
    }
  };

  const handleBack = () => {
    if (currentStep > 1) {
      setCurrentStep(currentStep - 1);
      animateContent(1);
    } else {
      router.back();
    }
  };

  return (
    <View
      className={`flex-1 ${
        resolvedTheme === "light" ? "bg-gray-50" : "bg-gray-950"
      }`}
    >
      <LinearGradient
        colors={
          resolvedTheme === "light"
            ? ["#f8fafc", "#e2e8f0", "#f1f5f9"]
            : ["#111827", "#1F2937"]
        }
        className="flex-1"
      >
        <View className="flex-1 px-4 pt-12">
          {/* Header */}
          <View className="flex-row items-center mb-8">
            <View className="flex-1">
              <Text
                className={`text-3xl font-bold ${
                  resolvedTheme === "light" ? "text-gray-800" : "text-white"
                }`}
              >
                {isProfileComplete ? "Edit Profile" : "Your Profile"}
              </Text>
              <Text
                className={`text-base mt-1 ${
                  resolvedTheme === "light" ? "text-gray-500" : "text-gray-400"
                }`}
              >
                {currentStep === 1
                  ? "Basic Information"
                  : currentStep === 2
                  ? "Height & Weight"
                  : currentStep === 3
                  ? "Activity Level"
                  : "Your Goals"}
              </Text>
            </View>
            <View className="flex-row items-center space-x-3">
              {[1, 2, 3, 4].map((step) => (
                <View
                  key={step}
                  className={`h-2.5 ${
                    currentStep === step
                      ? resolvedTheme === "light"
                        ? "w-6 bg-gray-800 rounded-full"
                        : "w-6 bg-white rounded-full"
                      : currentStep > step
                      ? resolvedTheme === "light"
                        ? "w-2.5 bg-gray-400 rounded-full"
                        : "w-2.5 bg-white/50 rounded-full"
                      : resolvedTheme === "light"
                      ? "w-2.5 bg-gray-200 rounded-full"
                      : "w-2.5 bg-white/10 rounded-full"
                  }`}
                />
              ))}
            </View>
          </View>

          {/* Content Area */}
          <ScrollView className="flex-1" showsVerticalScrollIndicator={false}>
            {currentStep === 1 && renderStep1()}
            {currentStep === 2 && renderStep2()}
            {currentStep === 3 && renderStep3()}
            {currentStep === 4 && renderStep4()}
          </ScrollView>

          {/* Separator line */}
          <View
            className={`h-px w-full mb-6 ${
              resolvedTheme === "light" ? "bg-gray-200" : "bg-white/10"
            }`}
          />

          {/* Bottom Navigation */}
          <View className="pb-8">
            <View className="flex-row space-x-4">
              {currentStep > 1 && (
                <TouchableOpacity
                  className={`flex-1 py-4 rounded-2xl border ${
                    resolvedTheme === "light"
                      ? "bg-gray-100 border-gray-200"
                      : "bg-white/5 border-white/10"
                  }`}
                  onPress={handleBack}
                >
                  <View className="flex-row items-center justify-center">
                    <Ionicons
                      name="arrow-back"
                      size={20}
                      color={resolvedTheme === "light" ? "#374151" : "white"}
                      style={{ marginRight: 6 }}
                    />
                    <Text
                      className={`text-center font-medium text-lg ${
                        resolvedTheme === "light"
                          ? "text-gray-800"
                          : "text-white"
                      }`}
                    >
                      Back
                    </Text>
                  </View>
                </TouchableOpacity>
              )}
              <TouchableOpacity
                className="flex-1 py-4 rounded-2xl"
                onPress={handleNext}
              >
                {isLoading ? (
                  <ActivityIndicator color="white" size="small" />
                ) : (
                  <View className="flex-row items-center justify-center">
                    <Text
                      className={`text-center font-semibold text-lg ${
                        resolvedTheme === "light"
                          ? "text-gray-800"
                          : "text-white"
                      }`}
                    >
                      {currentStep === 4 ? "Complete Setup" : "Continue"}
                    </Text>
                    {currentStep < 4 && (
                      <Ionicons
                        name="arrow-forward"
                        size={20}
                        color={resolvedTheme === "light" ? "#1f2937" : "white"}
                        style={{ marginLeft: 6 }}
                      />
                    )}
                  </View>
                )}
              </TouchableOpacity>
            </View>
          </View>
        </View>
      </LinearGradient>
    </View>
  );
}
