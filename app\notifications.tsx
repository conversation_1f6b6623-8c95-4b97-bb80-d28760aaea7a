import React, { useState, useEffect } from "react";
import {
  View,
  Text,
  ScrollView,
  TouchableOpacity,
  Switch,
  Alert,
  Animated,
} from "react-native";
import { useRouter, useNavigation } from "expo-router";
import { Ionicons } from "@expo/vector-icons";
import { LinearGradient } from "expo-linear-gradient";
import * as Haptics from "expo-haptics";

import {
  useNotifications,
  NotificationSchedule,
} from "@/context/NotificationsContext";

export default function NotificationsScreen() {
  const router = useRouter();
  const navigation = useNavigation();
  const {
    settings,
    hasPermission,
    requestPermissions,
    toggleNotifications,
    toggleSchedule,
  } = useNotifications();

  // Animation refs
  const fadeAnim = React.useRef(new Animated.Value(0)).current;
  const slideAnim = React.useRef(new Animated.Value(20)).current;

  // Animate content when component mounts
  useEffect(() => {
    Animated.parallel([
      Animated.spring(fadeAnim, {
        toValue: 1,
        tension: 50,
        friction: 7,
        useNativeDriver: true,
      }),
      Animated.spring(slideAnim, {
        toValue: 0,
        tension: 50,
        friction: 7,
        useNativeDriver: true,
      }),
    ]).start();

    // Set navigation title
    navigation.setOptions({
      title: "Notifications",
    });
  }, []);

  const handleMasterToggle = async (value: boolean) => {
    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);

    if (value && !hasPermission) {
      // Request permissions if needed
      const granted = await requestPermissions();
      if (!granted) {
        Alert.alert(
          "Permission Required",
          "You need to enable notifications permission in your device settings to use this feature.",
          [{ text: "OK" }]
        );
        return;
      }
    }

    toggleNotifications(value);
  };

  const handleScheduleToggle = (
    schedule: NotificationSchedule,
    enabled: boolean
  ) => {
    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
    toggleSchedule(schedule.id, enabled);
  };

  const formatTime = (hour: number, minute: number) => {
    const period = hour >= 12 ? "PM" : "AM";
    const adjustedHour = hour % 12 || 12;
    const formattedMinute = minute < 10 ? `0${minute}` : minute;
    return `${adjustedHour}:${formattedMinute} ${period}`;
  };

  const formatDays = (days: number[]) => {
    const dayNames = ["Mon", "Tue", "Wed", "Thu", "Fri", "Sat", "Sun"];

    if (days.length === 7) {
      return "Every day";
    }

    if (
      days.length === 5 &&
      [1, 2, 3, 4, 5].every((day) => days.includes(day))
    ) {
      return "Weekdays";
    }

    if (days.length === 2 && [6, 7].every((day) => days.includes(day))) {
      return "Weekends";
    }

    return days.map((day) => dayNames[day - 1]).join(", ");
  };

  const getNotificationIcon = (type: string) => {
    switch (type) {
      case "food_logging":
        return "fast-food";
      case "weight_tracking":
        return "trending-up";
      case "exercise_reminder":
        return "fitness";
      case "water_reminder":
        return "water";
      case "progress_summary":
        return "stats-chart";
      default:
        return "notifications";
    }
  };

  return (
    <View className="flex-1 bg-[#0A0A0A]">
      <LinearGradient colors={["#1A1A1A", "#0A0A0A"]} className="flex-1">
        <ScrollView className="flex-1 p-5">
          {/* Header */}
          <View className="flex-row justify-between items-center mb-6">
            <TouchableOpacity
              onPress={() => router.back()}
              className="bg-white/10 p-2 rounded-full"
            >
              <Ionicons name="arrow-back" size={24} color="white" />
            </TouchableOpacity>
            <Text className="text-2xl font-bold text-white">Notifications</Text>
            <View className="w-10" /> {/* Empty view for balanced layout */}
          </View>

          <Animated.View
            style={{
              opacity: fadeAnim,
              transform: [{ translateY: slideAnim }],
            }}
            className="space-y-6"
          >
            {/* Master toggle */}
            <View className="bg-white/5 rounded-3xl p-6 shadow-lg">
              <View className="flex-row justify-between items-center">
                <View className="flex-row items-center space-x-3">
                  <View className="w-10 h-10 bg-blue-500/20 rounded-full items-center justify-center">
                    <Ionicons name="notifications" size={20} color="#60a5fa" />
                  </View>
                  <View>
                    <Text className="text-xl font-semibold text-white">
                      Enable Notifications
                    </Text>
                    <Text className="text-gray-400 text-sm">
                      Turn on reminders and updates
                    </Text>
                  </View>
                </View>
                <Switch
                  value={settings.enabled}
                  onValueChange={handleMasterToggle}
                  trackColor={{ false: "#3b3b3b", true: "#60a5fa" }}
                  thumbColor={settings.enabled ? "#fff" : "#f4f3f4"}
                  ios_backgroundColor="#3b3b3b"
                />
              </View>
            </View>

            {/* Permission Status */}
            {hasPermission === false && (
              <View className="bg-white/5 rounded-3xl p-6 shadow-lg">
                <View className="flex-row items-center space-x-3">
                  <View className="w-10 h-10 bg-red-500/20 rounded-full items-center justify-center">
                    <Ionicons name="alert-circle" size={20} color="#f87171" />
                  </View>
                  <View className="flex-1">
                    <Text className="text-lg font-semibold text-white">
                      Permission Required
                    </Text>
                    <Text className="text-gray-400 text-sm">
                      Please enable notifications in your device settings to
                      receive reminders.
                    </Text>
                  </View>
                </View>
                <TouchableOpacity
                  onPress={requestPermissions}
                  className="bg-gradient-to-r from-red-500 to-red-600 px-4 py-3 rounded-xl mt-4"
                >
                  <Text className="text-white font-semibold text-center">
                    Request Permission
                  </Text>
                </TouchableOpacity>
              </View>
            )}

            {/* Notification schedules */}
            <View className="bg-white/5 rounded-3xl p-6 shadow-lg">
              <Text className="text-xl font-semibold text-white mb-4">
                Notification Settings
              </Text>

              {settings.schedules.map((schedule) => (
                <View
                  key={schedule.id}
                  className="flex-row justify-between items-center py-4 border-b border-white/10"
                >
                  <View className="flex-row items-center space-x-3">
                    <View className="w-10 h-10 bg-purple-500/20 rounded-full items-center justify-center">
                      <Ionicons
                        name={getNotificationIcon(schedule.type)}
                        size={18}
                        color="#d8b4fe"
                      />
                    </View>
                    <View className="flex-1 pr-4">
                      <Text className="text-white font-semibold">
                        {schedule.title}
                      </Text>
                      <Text className="text-gray-400 text-sm">
                        {formatTime(schedule.time.hour, schedule.time.minute)} •{" "}
                        {formatDays(schedule.days)}
                      </Text>
                    </View>
                  </View>
                  <Switch
                    value={schedule.enabled}
                    onValueChange={(value) =>
                      handleScheduleToggle(schedule, value)
                    }
                    trackColor={{ false: "#3b3b3b", true: "#d8b4fe" }}
                    thumbColor={schedule.enabled ? "#fff" : "#f4f3f4"}
                    ios_backgroundColor="#3b3b3b"
                    disabled={!settings.enabled}
                  />
                </View>
              ))}

              <TouchableOpacity
                className="mt-6 bg-white/10 p-4 rounded-xl flex-row justify-center items-center"
                onPress={() =>
                  Alert.alert(
                    "Coming Soon",
                    "Custom notification creation will be available in a future update."
                  )
                }
              >
                <Ionicons
                  name="add-circle"
                  size={20}
                  color="white"
                  className="mr-2"
                />
                <Text className="text-white font-semibold ml-2">
                  Add Custom Reminder
                </Text>
              </TouchableOpacity>
            </View>

            {/* Info Section */}
            <View className="bg-white/5 rounded-3xl p-6 shadow-lg">
              <Text className="text-xl font-semibold text-white mb-4">
                About Notifications
              </Text>
              <Text className="text-gray-400 mb-3">
                Notifications help you stay on track with your health and
                fitness goals by reminding you to:
              </Text>
              <View className="space-y-2">
                <View className="flex-row items-center">
                  <Ionicons name="checkmark-circle" size={16} color="#60a5fa" />
                  <Text className="text-gray-300 ml-2">
                    Log your meals regularly
                  </Text>
                </View>
                <View className="flex-row items-center">
                  <Ionicons name="checkmark-circle" size={16} color="#60a5fa" />
                  <Text className="text-gray-300 ml-2">
                    Track your weight progress
                  </Text>
                </View>
                <View className="flex-row items-center">
                  <Ionicons name="checkmark-circle" size={16} color="#60a5fa" />
                  <Text className="text-gray-300 ml-2">
                    Remember to exercise
                  </Text>
                </View>
                <View className="flex-row items-center">
                  <Ionicons name="checkmark-circle" size={16} color="#60a5fa" />
                  <Text className="text-gray-300 ml-2">
                    Review your weekly progress
                  </Text>
                </View>
              </View>
            </View>
          </Animated.View>
        </ScrollView>
      </LinearGradient>
    </View>
  );
}
