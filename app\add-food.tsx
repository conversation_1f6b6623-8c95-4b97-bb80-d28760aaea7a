import React, { useState, useEffect } from "react";
import {
  View,
  Text,
  ScrollView,
  TouchableOpacity,
  Alert,
  Animated,
  ActivityIndicator,
  TextInput,
  Image,
} from "react-native";
import { useRouter, useLocalSearchParams } from "expo-router";
import { LinearGradient } from "expo-linear-gradient";
import { Ionicons } from "@expo/vector-icons";
import * as Haptics from "expo-haptics";
import { BlurView } from "expo-blur";
import { useTheme } from "@/context/ThemeContext";

import { useFoodLog, FoodItem } from "@/context/FoodLogContext";

export default function AddFoodScreen() {
  const router = useRouter();
  const params = useLocalSearchParams();
  const { addFoodItem } = useFoodLog();
  const { resolvedTheme } = useTheme();

  // Get date from params if provided, otherwise use today's date
  const selectedDate =
    params.date?.toString() || new Date().toISOString().split("T")[0];

  // Initialize form data from params if provided
  const [name, setName] = useState(params.name?.toString() || "");
  const [calories, setCalories] = useState(params.calories?.toString() || "");
  const [protein, setProtein] = useState(params.protein?.toString() || "");
  const [carbs, setCarbs] = useState(params.carbs?.toString() || "");
  const [fat, setFat] = useState(params.fat?.toString() || "");
  const [servingSize, setServingSize] = useState("1 serving");
  const [mealType, setMealType] = useState<FoodItem["mealType"]>("lunch");
  const [imageUri, setImageUri] = useState<string | undefined>(
    params.image?.toString()
  );

  const [isLoading, setIsLoading] = useState(false);

  // Animation values
  const fadeAnim = React.useRef(new Animated.Value(0)).current;
  const slideAnim = React.useRef(new Animated.Value(20)).current;

  useEffect(() => {
    animateContent(1);
  }, []);

  const animateContent = (toValue: number) => {
    Animated.parallel([
      Animated.spring(fadeAnim, {
        toValue,
        tension: 50,
        friction: 7,
        useNativeDriver: true,
      }),
      Animated.spring(slideAnim, {
        toValue: toValue === 1 ? 0 : 20,
        tension: 50,
        friction: 7,
        useNativeDriver: true,
      }),
    ]).start();
  };

  const handleAddFood = async () => {
    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);

    // Validate inputs
    if (!name || !calories) {
      Alert.alert(
        "Missing Information",
        "Please provide at least a name and calories"
      );
      return;
    }

    try {
      setIsLoading(true);
      animateContent(0);

      // Short delay to ensure animation completes
      await new Promise((resolve) => setTimeout(resolve, 300));

      await addFoodItem({
        name,
        calories: parseInt(calories) || 0,
        protein: parseInt(protein) || 0,
        carbs: parseInt(carbs) || 0,
        fat: parseInt(fat) || 0,
        servingSize,
        image: imageUri,
        date: selectedDate,
        mealType,
      });

      // Navigate back to home screen
      router.push(`/(tabs)?date=${selectedDate}`);
    } catch (error) {
      console.error("Error adding food:", error);
      Alert.alert("Error", "Failed to add food. Please try again.");
    } finally {
      setIsLoading(false);
    }
  };

  // Updated StyledInput for profile-style fields (no border, subtle bg)
  const StyledInput = ({
    label,
    placeholder,
    value,
    onChangeText,
    keyboardType = "default",
  }: any) => (
    <View>
      <Text
        className={
          resolvedTheme === "light"
            ? "text-gray-700 mb-1"
            : "text-gray-300 mb-1"
        }
      >
        {label}
      </Text>
      <View
        className={`${
          resolvedTheme === "light"
            ? "bg-gray-100/60 border border-gray-200/60"
            : "bg-black/20 border border-gray-700"
        } rounded-xl p-1 h-14 mt-1`}
      >
        <TextInput
          className={`bg-transparent text-lg px-4 py-2 h-full ${
            resolvedTheme === "light" ? "text-gray-800" : "text-white"
          }`}
          placeholderTextColor={resolvedTheme === "light" ? "#888" : "#777"}
          value={value}
          onChangeText={onChangeText}
          placeholder={placeholder}
          keyboardType={keyboardType}
        />
      </View>
    </View>
  );

  return (
    <View
      className={`flex-1 ${
        resolvedTheme === "light" ? "bg-gray-50" : "bg-gray-950"
      }`}
    >
      <LinearGradient
        colors={
          resolvedTheme === "light"
            ? ["#f8fafc", "#e2e8f0", "#f1f5f9"]
            : ["#111827", "#1F2937"]
        }
        className="flex-1"
      >
        <View className="flex-1 px-4 pt-12">
          {/* Header */}
          <View className="flex-row items-center mb-8">
            <TouchableOpacity
              onPress={() => router.push(`/(tabs)?date=${selectedDate}`)}
              className={
                resolvedTheme === "light"
                  ? "bg-gray-100 p-3 rounded-full mr-4"
                  : "bg-white/10 p-3 rounded-full mr-4"
              }
            >
              <Ionicons
                name="arrow-back"
                size={22}
                color={resolvedTheme === "light" ? "#1f2937" : "white"}
              />
            </TouchableOpacity>
            <View className="flex-1">
              <Text
                className={`text-3xl font-bold ${
                  resolvedTheme === "light" ? "text-gray-800" : "text-white"
                }`}
              >
                Add Food
              </Text>
              <Text
                className={`${
                  resolvedTheme === "light" ? "text-gray-500" : "text-gray-400"
                } text-base mt-1`}
              >
                Add a new food to your daily log
              </Text>
            </View>
          </View>

          {/* Content Area */}
          <ScrollView className="flex-1" showsVerticalScrollIndicator={false}>
            <Animated.View
              style={{
                opacity: fadeAnim,
                transform: [{ translateY: slideAnim }],
              }}
              className="w-full"
            >
              {imageUri && (
                <View
                  className={`rounded-3xl p-2 mb-6 backdrop-blur-xl border ${
                    resolvedTheme === "light"
                      ? "bg-gray-100/60 border-gray-200/60"
                      : "bg-black/20 border-gray-700"
                  }`}
                >
                  <Image
                    source={{ uri: imageUri }}
                    className="w-full h-48 rounded-2xl"
                    resizeMode="cover"
                  />
                </View>
              )}

              {isLoading ? (
                <View className="items-center py-12">
                  <ActivityIndicator
                    size="large"
                    color={resolvedTheme === "light" ? "#1f2937" : "#FFFFFF"}
                  />
                  <Text
                    className={`${
                      resolvedTheme === "light"
                        ? "text-gray-500"
                        : "text-gray-400"
                    } mt-2`}
                  >
                    Adding food to log...
                  </Text>
                </View>
              ) : (
                <View
                  className={`rounded-3xl p-6 mb-6 backdrop-blur-xl ${
                    resolvedTheme === "light"
                      ? "bg-white border border-gray-200/60"
                      : "bg-gray-800 border border-gray-700"
                  }`}
                >
                  <View>
                    <StyledInput
                      label="Food Name"
                      placeholder="e.g., Grilled Chicken Salad"
                      value={name}
                      onChangeText={setName}
                    />

                    <View className="mt-2">
                      <StyledInput
                        label="Serving Size"
                        placeholder="e.g., 1 cup, 100g"
                        value={servingSize}
                        onChangeText={setServingSize}
                      />

                      <View className="mt-2">
                        <StyledInput
                          label="Calories"
                          placeholder="e.g., 250"
                          value={calories}
                          onChangeText={setCalories}
                          keyboardType="numeric"
                        />
                      </View>
                    </View>

                    <View className="mt-2">
                      <Text
                        className={
                          resolvedTheme === "light"
                            ? "text-gray-700 mb-1"
                            : "text-gray-300 mb-1"
                        }
                      >
                        Macronutrients (g)
                      </Text>
                      <View className="flex-row" style={{ marginTop: 8 }}>
                        <View className="flex-1 mr-2">
                          <View
                            className={`rounded-xl p-1 h-14 border ${
                              resolvedTheme === "light"
                                ? "bg-gray-100/60 border border-gray-200/60"
                                : "bg-black/20 border border-gray-700"
                            }`}
                          >
                            <TextInput
                              className={`bg-transparent text-lg px-4 py-2 h-full ${
                                resolvedTheme === "light"
                                  ? "text-gray-800"
                                  : "text-white"
                              }`}
                              placeholderTextColor={
                                resolvedTheme === "light" ? "#888" : "#777"
                              }
                              keyboardType="numeric"
                              value={protein}
                              onChangeText={setProtein}
                              placeholder="Protein"
                            />
                          </View>
                        </View>
                        <View className="flex-1 mr-2">
                          <View
                            className={`rounded-xl p-1 h-14 border ${
                              resolvedTheme === "light"
                                ? "bg-gray-100/60 border border-gray-200/60"
                                : "bg-black/20 border border-gray-700"
                            }`}
                          >
                            <TextInput
                              className={`bg-transparent text-lg px-4 py-2 h-full ${
                                resolvedTheme === "light"
                                  ? "text-gray-800"
                                  : "text-white"
                              }`}
                              placeholderTextColor={
                                resolvedTheme === "light" ? "#888" : "#777"
                              }
                              keyboardType="numeric"
                              value={carbs}
                              onChangeText={setCarbs}
                              placeholder="Carbs"
                            />
                          </View>
                        </View>
                        <View className="flex-1">
                          <View
                            className={`rounded-xl p-1 h-14 border ${
                              resolvedTheme === "light"
                                ? "bg-gray-100/60 border border-gray-200/60"
                                : "bg-black/20 border border-gray-700"
                            }`}
                          >
                            <TextInput
                              className={`bg-transparent text-lg px-4 py-2 h-full ${
                                resolvedTheme === "light"
                                  ? "text-gray-800"
                                  : "text-white"
                              }`}
                              placeholderTextColor={
                                resolvedTheme === "light" ? "#888" : "#777"
                              }
                              keyboardType="numeric"
                              value={fat}
                              onChangeText={setFat}
                              placeholder="Fat"
                            />
                          </View>
                        </View>
                      </View>
                    </View>

                    <View className="mt-2">
                      <Text
                        className={
                          resolvedTheme === "light"
                            ? "text-gray-700 mb-1"
                            : "text-gray-300 mb-1"
                        }
                      >
                        Meal Type
                      </Text>
                      <View className="flex-row">
                        {["breakfast", "lunch", "dinner", "snack"].map(
                          (meal, index, array) => (
                            <TouchableOpacity
                              key={meal}
                              onPress={() => {
                                Haptics.impactAsync(
                                  Haptics.ImpactFeedbackStyle.Light
                                );
                                setMealType(meal as FoodItem["mealType"]);
                              }}
                              style={{
                                flex: 1,
                                marginRight: index < array.length - 1 ? 4 : 0,
                              }}
                              className={`py-4 rounded-xl flex-row justify-center items-center border ${
                                mealType === meal
                                  ? resolvedTheme === "light"
                                    ? "bg-gray-100/60 border-gray-400"
                                    : "bg-black/20 border-gray-600"
                                  : resolvedTheme === "light"
                                  ? "bg-gray-100/60 border border-gray-200/60"
                                  : "bg-black/20 border border-gray-700"
                              }`}
                            >
                              <Text
                                className={`${
                                  mealType === meal
                                    ? resolvedTheme === "light"
                                      ? "text-gray-800 font-semibold"
                                      : "text-white font-semibold"
                                    : resolvedTheme === "light"
                                    ? "text-gray-500"
                                    : "text-gray-400"
                                } capitalize`}
                              >
                                {meal}
                              </Text>
                            </TouchableOpacity>
                          )
                        )}
                      </View>
                    </View>
                  </View>
                </View>
              )}
            </Animated.View>
          </ScrollView>

          {/* Separator line */}
          <View
            className={`h-px w-full mb-6 ${
              resolvedTheme === "light" ? "bg-gray-200/60" : "bg-white/10"
            }`}
          />

          {/* Bottom Button */}
          <View className="pb-8">
            <TouchableOpacity
              className="py-4 rounded-2xl"
              onPress={handleAddFood}
            >
              {isLoading ? (
                <ActivityIndicator
                  color={resolvedTheme === "light" ? "#1f2937" : "white"}
                  size="small"
                />
              ) : (
                <View className="flex-row items-center justify-center">
                  <Text
                    className={`text-center font-semibold text-lg ${
                      resolvedTheme === "light" ? "text-gray-800" : "text-white"
                    }`}
                  >
                    Save to Food Log
                  </Text>
                </View>
              )}
            </TouchableOpacity>
          </View>
        </View>
      </LinearGradient>
    </View>
  );
}
