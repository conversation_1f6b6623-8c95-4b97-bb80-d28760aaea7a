
-- Create profiles table
CREATE TABLE profiles (
  id UUID REFERENCES auth.users(id) PRIMARY KEY,
  device_id TEXT NOT NULL,
  weight FLOAT,
  height FLOAT,
  age INTEGER,
  gender TEXT,
  activity_level TEXT,
  goal_type TEXT,
  target_weight FLOAT,
  weight_change_rate FLOAT,
  daily_calorie_goal INTEGER,
  protein INTEGER,
  carbs INTEGER,
  fat INTEGER,
  initialized BOOLEAN DEFAULT false,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create food_logs table
CREATE TABLE food_logs (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  user_id UUID REFERENCES auth.users(id) NOT NULL,
  name TEXT NOT NULL,
  calories FLOAT NOT NULL,
  protein FLOAT NOT NULL,
  carbs FLOAT NOT NULL,
  fat FLOAT NOT NULL,
  serving_size TEXT,
  image_url TEXT,
  date DATE NOT NULL,
  meal_type TEXT NOT NULL,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create exercise_logs table
CREATE TABLE exercise_logs (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  user_id UUID REFERENCES auth.users(id) NOT NULL,
  name TEXT NOT NULL,
  date DATE NOT NULL,
  duration FLOAT NOT NULL,
  calories_burned FLOAT NOT NULL,
  exercise_type TEXT NOT NULL,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create weight_history table
CREATE TABLE weight_history (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  user_id UUID REFERENCES auth.users(id) NOT NULL,
  date DATE NOT NULL,
  weight FLOAT NOT NULL,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  UNIQUE(user_id, date)
);

-- Create trigger function to update the updated_at column
CREATE OR REPLACE FUNCTION update_updated_at()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = NOW();
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create trigger for profiles table
CREATE TRIGGER update_profiles_updated_at
BEFORE UPDATE ON profiles
FOR EACH ROW
EXECUTE FUNCTION update_updated_at();

-- Enable RLS on tables
ALTER TABLE profiles ENABLE ROW LEVEL SECURITY;
ALTER TABLE food_logs ENABLE ROW LEVEL SECURITY;
ALTER TABLE exercise_logs ENABLE ROW LEVEL SECURITY;
ALTER TABLE weight_history ENABLE ROW LEVEL SECURITY;

-- Create policies for profiles table
CREATE POLICY "Users can view their own profile"
ON profiles FOR SELECT
USING (auth.uid() = id);

CREATE POLICY "Users can update their own profile"
ON profiles FOR UPDATE
USING (auth.uid() = id);

CREATE POLICY "Users can insert their own profile"
ON profiles FOR INSERT
WITH CHECK (auth.uid() = id);

-- Create policies for food_logs table
CREATE POLICY "Users can view their own food logs"
ON food_logs FOR SELECT
USING (auth.uid() = user_id);

CREATE POLICY "Users can insert their own food logs"
ON food_logs FOR INSERT
WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update their own food logs"
ON food_logs FOR UPDATE
USING (auth.uid() = user_id);

CREATE POLICY "Users can delete their own food logs"
ON food_logs FOR DELETE
USING (auth.uid() = user_id);

-- Create policies for exercise_logs table
CREATE POLICY "Users can view their own exercise logs"
ON exercise_logs FOR SELECT
USING (auth.uid() = user_id);

CREATE POLICY "Users can insert their own exercise logs"
ON exercise_logs FOR INSERT
WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update their own exercise logs"
ON exercise_logs FOR UPDATE
USING (auth.uid() = user_id);

CREATE POLICY "Users can delete their own exercise logs"
ON exercise_logs FOR DELETE
USING (auth.uid() = user_id);

-- Create policies for weight_history table
CREATE POLICY "Users can view their own weight history"
ON weight_history FOR SELECT
USING (auth.uid() = user_id);

CREATE POLICY "Users can insert their own weight history"
ON weight_history FOR INSERT
WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update their own weight history"
ON weight_history FOR UPDATE
USING (auth.uid() = user_id);

CREATE POLICY "Users can delete their own weight history"
ON weight_history FOR DELETE
USING (auth.uid() = user_id);

-- Create index for faster querying by user_id and date
CREATE INDEX food_logs_user_id_date_idx ON food_logs(user_id, date);
CREATE INDEX exercise_logs_user_id_date_idx ON exercise_logs(user_id, date);
CREATE INDEX weight_history_user_id_date_idx ON weight_history(user_id, date); 