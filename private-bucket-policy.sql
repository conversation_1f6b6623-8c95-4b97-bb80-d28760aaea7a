-- Ensure the images bucket is private (better security)
UPDATE storage.buckets
SET public = false
WHERE name = 'images';

-- Make sure R<PERSON> is enabled
ALTER TABLE storage.objects ENABLE ROW LEVEL SECURITY;

-- Clear existing policies for this bucket to avoid conflicts
DROP POLICY IF EXISTS "Allow public read access for images bucket" ON storage.objects;
DROP POLICY IF EXISTS "Allow authenticated users to upload to images bucket" ON storage.objects;
DROP POLICY IF EXISTS "Allow users to update their own files" ON storage.objects;
DROP POLICY IF EXISTS "Allow users to delete their own files" ON storage.objects;
DROP POLICY IF EXISTS "Allow users to read their own files" ON storage.objects;
DROP POLICY IF EXISTS "Allow users to upload files to their own folder" ON storage.objects;

-- Since we're using device ID not user ID, we need to make policies more permissive
-- Add a policy that allows signed-in users to read from any folder
-- This is required because we don't know the device ID at query time in SQL
CREATE POLICY "Allow authenticated users to read from images bucket"
ON storage.objects
FOR SELECT
TO authenticated
USING (bucket_id = 'images');

-- Allow authenticated users to upload to images bucket
CREATE POLICY "Allow authenticated users to upload to images bucket"
ON storage.objects
FOR INSERT
TO authenticated
WITH CHECK (bucket_id = 'images');

-- Allow authenticated users to update files in the images bucket
CREATE POLICY "Allow authenticated users to update files in images bucket"
ON storage.objects
FOR UPDATE
TO authenticated
USING (bucket_id = 'images');

-- Allow authenticated users to delete files in the images bucket
CREATE POLICY "Allow authenticated users to delete files in images bucket"
ON storage.objects
FOR DELETE
TO authenticated
USING (bucket_id = 'images'); 