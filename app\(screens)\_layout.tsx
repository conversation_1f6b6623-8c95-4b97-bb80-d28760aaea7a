import { Stack } from "expo-router";

export default function ScreensLayout() {
  return (
    <Stack>
      <Stack.Screen name="profile" options={{ headerShown: false }} />
      <Stack.Screen name="add-food" options={{ headerShown: false }} />
      <Stack.Screen name="add-exercise" options={{ headerShown: false }} />
      <Stack.Screen name="add-weight" options={{ headerShown: false }} />
      <Stack.Screen name="notifications" options={{ headerShown: false }} />
      <Stack.Screen name="barcode-scanner" options={{ headerShown: false }} />
      <Stack.Screen name="social" options={{ headerShown: false }} />
    </Stack>
  );
}
