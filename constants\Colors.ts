/**
 * Below are the colors that are used in the app. The colors are defined in the light and dark mode.
 * There are many other ways to style your app. For example, [Nativewind](https://www.nativewind.dev/), [Tamagui](https://tamagui.dev/), [unistyles](https://reactnativeunistyles.vercel.app), etc.
 */

const tintColorLight = "#4b5563"; // Minimal gray that matches our theme
const tintColorDark = "#fff";

export const Colors = {
  light: {
    text: "#374151",
    background: "#fafafa",
    tint: tintColorLight,
    icon: "#9ca3af",
    tabIconDefault: "#9ca3af",
    tabIconSelected: tintColorLight,
  },
  dark: {
    text: "#ECEDEE",
    background: "#151718",
    tint: tintColorDark,
    icon: "#9BA1A6",
    tabIconDefault: "#9BA1A6",
    tabIconSelected: tintColorDark,
  },
};
