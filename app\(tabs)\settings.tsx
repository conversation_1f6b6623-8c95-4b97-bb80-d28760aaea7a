import React, { useEffect } from "react";
import {
  View,
  Text,
  ScrollView,
  TouchableOpacity,
  Alert,
  Animated,
  Switch,
  Linking,
  Platform,
} from "react-native";
import { useRouter } from "expo-router";
import { Ionicons } from "@expo/vector-icons";
import { LinearGradient } from "expo-linear-gradient";
import * as Haptics from "expo-haptics";
import { Image } from "expo-image";

import { useUserProfile, UserProfile } from "@/context/UserProfileContext";
import { useNotifications } from "@/context/NotificationsContext";
import { useTheme } from "@/context/ThemeContext";
import { getShadowStyle } from "@/utils/shadowStyles";

// Extended UserProfile interface to include the additional properties we need
interface ExtendedUserProfile extends UserProfile {
  weightUnit?: string;
  name?: string;
  goal?: string;
}

export default function SettingsScreen() {
  const router = useRouter();
  const { profile } = useUserProfile();
  const { theme, setTheme, resolvedTheme } = useTheme();
  const extendedProfile: ExtendedUserProfile = {
    ...profile,
    weightUnit: "kg",
    name: "",
    goal: "",
  };
  const { settings: notificationSettings, toggleNotifications } =
    useNotifications();

  // Animation refs
  const fadeAnim = React.useRef(new Animated.Value(0)).current;
  const slideAnim = React.useRef(new Animated.Value(20)).current;

  // Animate content when component mounts
  useEffect(() => {
    Animated.parallel([
      Animated.spring(fadeAnim, {
        toValue: 1,
        tension: 50,
        friction: 7,
        useNativeDriver: true,
      }),
      Animated.spring(slideAnim, {
        toValue: 0,
        tension: 50,
        friction: 7,
        useNativeDriver: true,
      }),
    ]).start();
  }, []);

  const handleItemPress = (action: () => void) => {
    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
    action();
  };

  const settings = [
    {
      icon: "person-circle",
      color: "#60a5fa",
      title: "My Profile",
      description: "View and edit your profile details",
      onPress: () => router.push("../profile"),
    },
    {
      icon: "notifications",
      color: "#f472b6",
      title: "Notifications",
      description: "Manage your reminders and alerts",
      onPress: () => router.push("../notifications"),
    },
    {
      icon: "barcode-outline",
      color: "#a78bfa",
      title: "Scan Barcode",
      description: "Scan a barcode to log food",
      onPress: () => router.push("../scanner"),
    },
    {
      icon: "people",
      color: "#4ade80",
      title: "Social",
      description: "Connect with friends and share progress",
      onPress: () => router.push("../social"),
    },
    {
      icon: "fitness",
      color: "#fb923c",
      title: "Exercise Tracker",
      description: "Log and monitor your workouts",
      onPress: () => router.push("../add-exercise"),
    },
    {
      icon: "trending-up",
      color: "#f87171",
      title: "Weight Tracker",
      description: "Record and visualize your weight journey",
      onPress: () => router.push("../add-weight"),
    },
  ];

  const generalSettings = [
    {
      icon: "moon",
      color: "#818cf8",
      title: "Dark Mode",
      isToggle: true,
      toggled: resolvedTheme === "dark",
      onToggle: (value: boolean) => {
        Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
        setTimeout(() => {
          setTheme(value ? "dark" : "light");
        }, 0);
      },
    },
    {
      icon: "notifications",
      color: "#f472b6",
      title: "Notifications",
      isToggle: true,
      toggled: notificationSettings.enabled,
      onToggle: (value: boolean) => {
        Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
        toggleNotifications(value);
      },
    },
    {
      icon: "globe",
      color: "#10b981",
      title: "Language",
      value: "English",
      onPress: () => {
        Alert.alert(
          "Coming Soon",
          "Language settings will be available in a future update."
        );
      },
    },
    {
      icon: "calculator",
      color: "#f59e0b",
      title: "Units of Measurement",
      value: extendedProfile.weightUnit === "kg" ? "Metric" : "Imperial",
      onPress: () => router.push("../profile"),
    },
  ];

  const aboutSettings = [
    {
      icon: "information-circle",
      color: "#60a5fa",
      title: "About Calorie AI",
      onPress: () => {
        Alert.alert(
          "About Calorie AI",
          "Version 1.0.0\n\nCalorie AI is a comprehensive nutrition and fitness tracking app designed to help you achieve your health goals.",
          [{ text: "OK" }]
        );
      },
    },
    {
      icon: "shield-checkmark",
      color: "#10b981",
      title: "Privacy Policy",
      onPress: () => {
        Linking.openURL("https://example.com/privacy-policy");
      },
    },
    {
      icon: "document-text",
      color: "#f59e0b",
      title: "Terms of Service",
      onPress: () => {
        Linking.openURL("https://example.com/terms-of-service");
      },
    },
    {
      icon: "mail",
      color: "#f87171",
      title: "Contact Support",
      onPress: () => {
        Linking.openURL("mailto:<EMAIL>");
      },
    },
  ];

  const renderSettingItem = (item: any, index: number, isLast: boolean) => (
    <TouchableOpacity
      key={index}
      onPress={() => item.onPress && handleItemPress(item.onPress)}
      disabled={item.isToggle}
      className={`flex-row items-center justify-between py-4 ${
        !isLast
          ? `border-b ${
              resolvedTheme === "light"
                ? "border-gray-200/60"
                : "border-gray-600/50"
            }`
          : ""
      }`}
      style={{ opacity: item.disabled ? 0.5 : 1 }}
    >
      <View className="flex-row items-center">
        <View
          className="w-10 h-10 rounded-full items-center justify-center mr-4"
          style={{ backgroundColor: `${item.color}20` }}
        >
          <Ionicons name={item.icon} size={20} color={item.color} />
        </View>
        <View>
          <Text
            className={`font-semibold ${
              resolvedTheme === "dark" ? "text-white" : "text-gray-800"
            }`}
          >
            {item.title}
          </Text>
          {item.description && (
            <Text
              className={`text-sm ${
                resolvedTheme === "dark" ? "text-gray-400" : "text-gray-500"
              }`}
            >
              {item.description}
            </Text>
          )}
        </View>
      </View>

      {item.isToggle ? (
        <Switch
          value={item.toggled}
          onValueChange={item.onToggle}
          trackColor={{
            false: resolvedTheme === "dark" ? "#3b3b3b" : "#e5e7eb",
            true: item.color,
          }}
          thumbColor={"#fff"}
          ios_backgroundColor={resolvedTheme === "dark" ? "#3b3b3b" : "#e5e7eb"}
        />
      ) : item.value ? (
        <View className="flex-row items-center">
          <Text
            className={`${
              resolvedTheme === "dark" ? "text-gray-400" : "text-gray-500"
            } mr-2`}
          >
            {item.value}
          </Text>
          <Ionicons
            name="chevron-forward"
            size={20}
            color={resolvedTheme === "dark" ? "#6b7280" : "#9ca3af"}
          />
        </View>
      ) : (
        <Ionicons
          name="chevron-forward"
          size={20}
          color={resolvedTheme === "dark" ? "#6b7280" : "#9ca3af"}
        />
      )}
    </TouchableOpacity>
  );

  return (
    <View
      className={`flex-1 ${
        resolvedTheme === "light" ? "bg-gray-50" : "bg-gray-950"
      }`}
    >
      <LinearGradient
        colors={
          resolvedTheme === "light"
            ? ["#f8fafc", "#e2e8f0", "#f1f5f9"]
            : ["#111827", "#1F2937"]
        }
        className="flex-1"
      >
        <ScrollView className="flex-1 px-5 pt-5">
          <View className="mb-6">
            {/* User profile card */}
            <View
              className={`rounded-3xl p-6 mb-6 backdrop-blur-xl border ${
                resolvedTheme === "light"
                  ? "bg-white border-gray-200/60"
                  : "bg-gray-800 border-gray-700"
              }`}
              style={getShadowStyle(resolvedTheme, "card")}
            >
              <TouchableOpacity
                onPress={() => handleItemPress(() => router.push("../profile"))}
                className="flex-row items-center"
              >
                <View
                  className={`w-20 h-20 rounded-xl items-center justify-center ${
                    resolvedTheme === "light"
                      ? "bg-indigo-100/60"
                      : "bg-indigo-500/20"
                  }`}
                >
                  <Ionicons name="person" size={36} color="#818cf8" />
                </View>
                <View className="ml-4 flex-1">
                  <Text
                    className={`text-xl font-bold ${
                      resolvedTheme === "dark" ? "text-white" : "text-gray-800"
                    }`}
                  >
                    {extendedProfile.name || "Your Profile"}
                  </Text>
                  <Text
                    className={`${
                      resolvedTheme === "dark"
                        ? "text-gray-400"
                        : "text-gray-500"
                    } mt-1`}
                  >
                    {extendedProfile.weight
                      ? `${extendedProfile.weight} ${extendedProfile.weightUnit} • `
                      : ""}
                    {extendedProfile.goal || "Set your goals"}
                  </Text>

                  <View className="flex-row mt-2">
                    <View
                      className={`rounded-full px-3 py-1 mr-2 ${
                        resolvedTheme === "light"
                          ? "bg-indigo-100/60"
                          : "bg-indigo-500/20"
                      }`}
                    >
                      <Text
                        className={`text-sm ${
                          resolvedTheme === "light"
                            ? "text-indigo-600"
                            : "text-indigo-400"
                        }`}
                      >
                        Edit Profile
                      </Text>
                    </View>
                  </View>
                </View>
                <View className="ml-auto">
                  <Ionicons
                    name="chevron-forward"
                    size={24}
                    color={resolvedTheme === "dark" ? "#6b7280" : "#9ca3af"}
                  />
                </View>
              </TouchableOpacity>
            </View>

            {/* Features section */}
            <View
              className={`rounded-3xl p-6 mb-5 backdrop-blur-xl border ${
                resolvedTheme === "light"
                  ? "bg-white border-gray-200/60"
                  : "bg-gray-800 border-gray-700"
              }`}
              style={getShadowStyle(resolvedTheme, "card")}
            >
              <Text
                className={`text-lg font-semibold ${
                  resolvedTheme === "dark" ? "text-white" : "text-gray-800"
                } mb-4`}
              >
                Features
              </Text>
              {settings.map((item, index) =>
                renderSettingItem(item, index, index === settings.length - 1)
              )}
            </View>

            {/* General settings section */}
            <View
              className={`rounded-3xl p-6 mb-5 backdrop-blur-xl border ${
                resolvedTheme === "light"
                  ? "bg-white border-gray-200/60"
                  : "bg-gray-800 border-gray-700"
              }`}
              style={getShadowStyle(resolvedTheme, "card")}
            >
              <Text
                className={`text-lg font-semibold ${
                  resolvedTheme === "dark" ? "text-white" : "text-gray-800"
                } mb-4`}
              >
                Preferences
              </Text>
              {generalSettings.map((item, index) =>
                renderSettingItem(
                  item,
                  index,
                  index === generalSettings.length - 1
                )
              )}
            </View>

            {/* About section */}
            <View
              className={`rounded-3xl p-6 mb-5 backdrop-blur-xl border ${
                resolvedTheme === "light"
                  ? "bg-white border-gray-200/60"
                  : "bg-gray-800 border-gray-700"
              }`}
              style={getShadowStyle(resolvedTheme, "card")}
            >
              <Text
                className={`text-lg font-semibold ${
                  resolvedTheme === "dark" ? "text-white" : "text-gray-800"
                } mb-4`}
              >
                About
              </Text>
              {aboutSettings.map((item, index) =>
                renderSettingItem(
                  item,
                  index,
                  index === aboutSettings.length - 1
                )
              )}
            </View>

            {/* Logout and app version */}
            <View className="space-y-3 mb-10">
              <TouchableOpacity
                onPress={() => {
                  Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);
                  Alert.alert("Logout", "Are you sure you want to logout?", [
                    { text: "Cancel", style: "cancel" },
                    { text: "Logout", style: "destructive" },
                  ]);
                }}
                className={`rounded-xl p-4 border items-center ${
                  resolvedTheme === "light"
                    ? "bg-red-50 border-red-200/60"
                    : "bg-red-900/20 border-red-900/30"
                }`}
              >
                <Text className="text-red-500 font-semibold text-center">
                  Logout
                </Text>
              </TouchableOpacity>

              <Text className="text-gray-500 text-center text-xs mt-4">
                Calorie AI v1.0.0
              </Text>
            </View>
          </View>
        </ScrollView>
      </LinearGradient>
    </View>
  );
}
