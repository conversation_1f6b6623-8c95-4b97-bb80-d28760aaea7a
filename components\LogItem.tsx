import React from "react";
import { View, Text, TouchableOpacity, Alert } from "react-native";
import Animated, { LinearTransition, ZoomIn } from "react-native-reanimated";
import { Ionicons } from "@expo/vector-icons";
import { Image } from "expo-image";
import * as Haptics from "expo-haptics";
import { useTheme } from "@/context/ThemeContext";
import { getShadowStyle } from "@/utils/shadowStyles";

interface LogItemProps {
  item: any;
  selectedDate: string;
  deleteFoodItem: (id: string, date: string) => Promise<void>;
  deleteExercise: (id: string, date: string) => Promise<void>;
  setForceUpdate: (value: React.SetStateAction<number>) => void;
}

export function LogItem({
  item,
  selectedDate,
  deleteFoodItem,
  deleteExercise,
  setForceUpdate,
}: LogItemProps) {
  const { resolvedTheme } = useTheme();

  return (
    <Animated.View
      layout={LinearTransition.springify()}
      entering={ZoomIn.duration(300)}
      className={`rounded-2xl overflow-hidden flex-row border mb-3 ${
        resolvedTheme === "light"
          ? item.itemType === "exercise"
            ? "bg-blue-50/60 border-blue-200/50"
            : "bg-white border-gray-200/40"
          : item.itemType === "exercise"
          ? "bg-blue-900 border-blue-800"
          : "bg-gray-800 border-gray-700"
      }`}
      style={getShadowStyle(resolvedTheme, "card")}
    >
      {/* Image/Icon Area - Enhanced for exercise */}
      {item.itemType === "food" && (item.image || item.imageUri) ? (
        <Image
          source={{ uri: item.image || item.imageUri }}
          style={{ width: 96, height: "100%" }}
          contentFit="cover"
          transition={100}
          cachePolicy="memory-disk"
          placeholder={{
            uri: "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNk+P+/HgAFeAJ5jR4WaAAAAABJRU5ErkJggg==",
          }}
        />
      ) : item.itemType === "exercise" ? (
        <View
          className={`w-24 h-full items-center justify-center aspect-square ${
            resolvedTheme === "light"
              ? "bg-gradient-to-br from-blue-100/60 to-blue-200/40"
              : "bg-gradient-to-br from-indigo-500/30 to-purple-500/20"
          }`}
        >
          <View
            className={`w-16 h-16 rounded-full items-center justify-center ${
              resolvedTheme === "light" ? "bg-blue-200/50" : "bg-indigo-500/30"
            }`}
          >
            <Ionicons
              name={
                item.type === "cardio"
                  ? "fitness-outline"
                  : item.type === "strength"
                  ? "barbell-outline"
                  : item.type === "flexibility"
                  ? "body-outline"
                  : item.type === "sports"
                  ? "basketball-outline"
                  : "bicycle-outline"
              }
              size={32}
              color={resolvedTheme === "light" ? "#2563eb" : "#FFFFFF"}
            />
          </View>
        </View>
      ) : (
        <View
          className={`w-24 h-full items-center justify-center aspect-square ${
            resolvedTheme === "light" ? "bg-gray-100/60" : "bg-gray-700"
          }`}
        >
          <Ionicons
            name="nutrition-outline"
            size={32}
            color={resolvedTheme === "light" ? "#4b5563" : "#FFFFFF"}
          />
        </View>
      )}

      {/* Content Area - Enhanced for exercise */}
      <View className="flex-1 py-3 px-3">
        {/* Title with exercise icon for workout items */}
        <View className="flex-row items-center">
          <Text
            className={`${
              resolvedTheme === "light" ? "text-gray-700" : "text-gray-400"
            } text-base`}
          >
            {item.name.replace(/_/g, " ")}
          </Text>
        </View>

        {/* Calories and time row */}
        <View className="flex-row justify-between items-center mt-2">
          <View className="flex-row items-center">
            <Ionicons
              name={
                item.itemType === "exercise" ? "pulse-outline" : "flame-outline"
              }
              size={14}
              color={
                resolvedTheme === "light"
                  ? item.itemType === "exercise"
                    ? "#6366f1"
                    : "#ea580c"
                  : item.itemType === "exercise"
                  ? "#a78bfa"
                  : "#FF5722"
              }
            />
            <Text
              className={`text-[1.02rem] ml-1 ${
                resolvedTheme === "light" ? "text-gray-800" : "text-gray-100"
              }`}
            >
              {item.itemType === "food"
                ? `${item.calories} cal`
                : `${item.caloriesBurned} cal burned`}
            </Text>
          </View>
          <Text
            className={`text-sm ${
              resolvedTheme === "light" ? "text-gray-600" : "text-gray-500"
            }`}
          >
            {new Date(item.timestamp).toLocaleTimeString([], {
              hour: "2-digit",
              minute: "2-digit",
            })}
          </Text>
        </View>

        {/* Item-specific details */}
        {item.itemType === "food" ? (
          <View className="flex-row mt-2">
            <View
              className={`rounded-full px-2 py-0.5 mr-2 ${
                resolvedTheme === "light" ? "bg-red-100/60" : "bg-red-500/20"
              }`}
            >
              <Text
                className={`text-sm ${
                  resolvedTheme === "light" ? "text-red-700" : "text-red-400"
                }`}
              >
                P: {item.protein}g
              </Text>
            </View>
            <View
              className={`rounded-full px-2 py-0.5 mr-2 ${
                resolvedTheme === "light"
                  ? "bg-green-100/60"
                  : "bg-green-500/20"
              }`}
            >
              <Text
                className={`text-sm ${
                  resolvedTheme === "light"
                    ? "text-green-700"
                    : "text-green-400"
                }`}
              >
                C: {item.carbs}g
              </Text>
            </View>
            <View
              className={`rounded-full px-2 py-0.5 ${
                resolvedTheme === "light" ? "bg-blue-100/60" : "bg-blue-500/20"
              }`}
            >
              <Text
                className={`text-sm ${
                  resolvedTheme === "light" ? "text-sky-700" : "text-blue-400"
                }`}
              >
                F: {item.fat}g
              </Text>
            </View>
          </View>
        ) : (
          // Enhanced Exercise item details
          <View className="mt-2">
            <View className="flex-row items-center">
              <View
                className={`rounded-full px-3 py-1 mr-2 ${
                  resolvedTheme === "light"
                    ? "bg-blue-100/60"
                    : "bg-indigo-500/20"
                }`}
              >
                <Text
                  className={`text-sm capitalize ${
                    resolvedTheme === "light"
                      ? "text-sky-700"
                      : "text-indigo-300"
                  }`}
                >
                  {item.type}
                </Text>
              </View>
              <View
                className={`rounded-full px-3 py-1 flex-row items-center ${
                  resolvedTheme === "light"
                    ? "bg-gray-100/60"
                    : "bg-purple-500/20"
                }`}
              >
                <Ionicons
                  name="time-outline"
                  size={12}
                  color={resolvedTheme === "light" ? "#0891B2" : "#d8b4fe"}
                  style={{ marginRight: 4 }}
                />
                <Text
                  className={`text-sm ${
                    resolvedTheme === "light" ? "text-black" : "text-gray-500"
                  }`}
                >
                  {item.duration} min
                </Text>
              </View>
            </View>
          </View>
        )}
      </View>

      {/* Delete Button - Enhanced */}
      <TouchableOpacity
        onPress={() => {
          Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);
          Alert.alert(
            `Delete ${item.itemType === "food" ? "Food" : "Exercise"} Item`,
            `Are you sure you want to remove this ${item.itemType} from your log?`,
            [
              { text: "Cancel", style: "cancel" },
              {
                text: "Delete",
                style: "destructive",
                onPress: async () => {
                  if (item.itemType === "food") {
                    await deleteFoodItem(item.id, selectedDate);
                  } else if (item.itemType === "exercise") {
                    await deleteExercise(item.id, item.date);
                  }
                  setForceUpdate((prev) => prev + 1); // Trigger re-fetch/re-sort
                },
              },
            ]
          );
        }}
        className={`pr-3 self-center ${
          item.itemType === "exercise" ? "ml-1" : ""
        }`}
      >
        <Ionicons
          name="close-circle"
          size={22}
          color={resolvedTheme === "light" ? "#F87171" : "#FF6B6B"}
        />
      </TouchableOpacity>
    </Animated.View>
  );
}
