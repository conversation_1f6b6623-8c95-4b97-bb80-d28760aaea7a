# Calorie-AI with Supabase Integration

This is a React Native Expo app for calorie tracking, exercise logging, and weight monitoring. The app now features Supabase integration for cloud data storage.

## Features

- Track daily food intake and nutrition
- Log exercises and calculate calories burned
- Monitor weight changes over time
- Calculate BMR, TDEE, and calorie goals
- Store all data securely in Supabase
- Automatic data migration from local storage to cloud
- Device-based user authentication

## Getting Started

1. Install dependencies

   ```bash
   npm install
   ```

2. Set up your Supabase project (see [SUPABASE.md](./SUPABASE.md) for detailed instructions)

3. Create a `.env` file in the project root with your Supabase credentials:

   ```
   EXPO_PUBLIC_SUPABASE_URL=your_project_url
   EXPO_PUBLIC_SUPABASE_ANON_KEY=your_project_anon_key
   ```

4. Run the migration script in the Supabase SQL Editor (found in `scripts/supabase-migration.sql`)

5. Start the app

   ```bash
   npx expo start
   ```

## Database Integration

This app uses Supabase as its backend database. Key features:

- Cloud storage of all user data
- Row-level security for data protection
- Anonymous authentication tied to device ID
- Automatic local-to-cloud data migration
- Fallback to local storage when offline

For detailed information about the Supabase integration, see [SUPABASE.md](./SUPABASE.md).

## Data Models

- **User Profile**: Height, weight, age, gender, activity level, goals
- **Food Logs**: Food items with nutritional information
- **Exercise Logs**: Exercise activities with duration and calories burned
- **Weight History**: Weight measurements over time

## Technology Stack

- [React Native](https://reactnative.dev/)
- [Expo](https://expo.dev/)
- [Supabase](https://supabase.com/)
- [TypeScript](https://www.typescriptlang.org/)
- [NativeWind](https://www.nativewind.dev/) (TailwindCSS for React Native)

## Learn More

- [Supabase Documentation](https://supabase.com/docs)
- [Expo Documentation](https://docs.expo.dev/)
- [React Native Documentation](https://reactnative.dev/docs/getting-started)
