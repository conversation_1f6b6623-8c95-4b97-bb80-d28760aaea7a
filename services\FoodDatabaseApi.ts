// Food Database API integration using USDA FoodData Central API
// Documentation: https://fdc.nal.usda.gov/api-guide.html

// API key would normally be stored in environment variables
// For demonstration purposes, we'll keep it here
const API_KEY = "DEMO_KEY"; // Replace with your actual API key in production
const BASE_URL = "https://api.nal.usda.gov/fdc/v1";

export interface FoodSearchResult {
  fdcId: number;
  description: string;
  brandName?: string;
  ingredients?: string;
  servingSize?: number;
  servingSizeUnit?: string;
  foodCategory?: string;
}

export interface FoodNutrient {
  nutrientId: number;
  nutrientName: string;
  value: number;
  unitName: string;
}

export interface FoodDetails {
  fdcId: number;
  description: string;
  brandName?: string;
  ingredients?: string;
  servingSize?: number;
  servingSizeUnit?: string;
  foodCategory?: string;
  calories: number;
  protein: number; // in grams
  carbs: number; // in grams
  fat: number; // in grams
  foodNutrients: FoodNutrient[];
}

/**
 * Search for foods in the USDA database
 * @param query Search term
 * @param pageSize Number of results to return
 * @param pageNumber Page number for pagination
 * @returns Promise with search results
 */
export async function searchFoods(
  query: string,
  pageSize: number = 25,
  pageNumber: number = 1
): Promise<FoodSearchResult[]> {
  try {
    const response = await fetch(
      `${BASE_URL}/foods/search?api_key=${API_KEY}&query=${encodeURIComponent(
        query
      )}&pageSize=${pageSize}&pageNumber=${pageNumber}&dataType=Foundation,SR%20Legacy,Survey%20(FNDDS),Branded`
    );

    if (!response.ok) {
      throw new Error(`API Error: ${response.status}`);
    }

    const data = await response.json();
    return data.foods || [];
  } catch (error) {
    console.error("Error searching food database:", error);
    throw error;
  }
}

/**
 * Get detailed information about a specific food
 * @param fdcId Food Data Central ID
 * @returns Promise with food details
 */
export async function getFoodDetails(fdcId: number): Promise<FoodDetails> {
  try {
    const response = await fetch(
      `${BASE_URL}/food/${fdcId}?api_key=${API_KEY}`
    );

    if (!response.ok) {
      throw new Error(`API Error: ${response.status}`);
    }

    const data = await response.json();

    // Extract common nutrients
    const nutrients = data.foodNutrients || [];

    // Map nutrient IDs to common names
    // USDA nutrient IDs:
    // Energy (kcal): 1008
    // Protein: 1003
    // Carbohydrates: 1005
    // Total Fat: 1004
    const calories = findNutrientValue(nutrients, 1008);
    const protein = findNutrientValue(nutrients, 1003);
    const carbs = findNutrientValue(nutrients, 1005);
    const fat = findNutrientValue(nutrients, 1004);

    return {
      fdcId: data.fdcId,
      description: data.description,
      brandName: data.brandName,
      ingredients: data.ingredients,
      servingSize: data.servingSize,
      servingSizeUnit: data.servingSizeUnit,
      foodCategory: data.foodCategory,
      calories,
      protein,
      carbs,
      fat,
      foodNutrients: nutrients.map((n: any) => ({
        nutrientId: n.nutrient.id,
        nutrientName: n.nutrient.name,
        value: n.amount,
        unitName: n.nutrient.unitName,
      })),
    };
  } catch (error) {
    console.error("Error fetching food details:", error);
    throw error;
  }
}

/**
 * Search for foods by UPC/barcode
 * @param barcode UPC/barcode
 * @returns Promise with food details if found
 */
export async function searchByBarcode(
  barcode: string
): Promise<FoodDetails | null> {
  try {
    // First search for the food by barcode/UPC
    const response = await fetch(
      `${BASE_URL}/foods/search?api_key=${API_KEY}&query=${barcode}&dataType=Branded`
    );

    if (!response.ok) {
      throw new Error(`API Error: ${response.status}`);
    }

    const data = await response.json();

    // If food found, get detailed information
    if (data.foods && data.foods.length > 0) {
      const fdcId = data.foods[0].fdcId;
      return await getFoodDetails(fdcId);
    }

    return null;
  } catch (error) {
    console.error("Error searching by barcode:", error);
    throw error;
  }
}

/**
 * Helper function to find a nutrient value from the nutrients array
 * @param nutrients Array of nutrients
 * @param nutrientId ID of the nutrient to find
 * @returns Nutrient value or 0 if not found
 */
function findNutrientValue(nutrients: any[], nutrientId: number): number {
  const nutrient = nutrients.find(
    (n) => n.nutrient && n.nutrient.id === nutrientId
  );
  return nutrient ? nutrient.amount : 0;
}

/**
 * Get food suggestions based on user's dietary goals
 * @param goal Weight goal ('loss' | 'gain' | 'maintain')
 * @param category Optional food category
 * @returns Promise with suggested foods
 */
export async function getFoodSuggestions(
  goal: "loss" | "gain" | "maintain",
  category?: string
): Promise<FoodSearchResult[]> {
  try {
    // Define search parameters based on goals
    let query = "";

    switch (goal) {
      case "loss":
        query = "low calorie";
        if (category === "protein") query = "high protein low fat";
        if (category === "breakfast") query = "healthy breakfast";
        break;
      case "gain":
        query = "high calorie";
        if (category === "protein") query = "high protein";
        if (category === "snack") query = "protein snack";
        break;
      case "maintain":
        query = "balanced meal";
        break;
    }

    if (category && !["protein", "breakfast", "snack"].includes(category)) {
      query += ` ${category}`;
    }

    return await searchFoods(query);
  } catch (error) {
    console.error("Error getting food suggestions:", error);
    throw error;
  }
}
