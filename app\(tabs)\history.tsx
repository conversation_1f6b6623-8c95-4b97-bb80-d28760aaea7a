import React, { useEffect, useState } from "react";
import {
  ScrollView,
  TouchableOpacity,
  Image,
  View,
  Text,
  Animated,
  Alert,
  TextInput,
} from "react-native";
import { Ionicons } from "@expo/vector-icons";
import { LinearGradient } from "expo-linear-gradient";
import { BlurView } from "expo-blur";
import * as Haptics from "expo-haptics";
import { useRouter } from "expo-router";

import { useHistory } from "@/context/HistoryContext";
import { useUserProfile } from "@/context/UserProfileContext";
import { useFoodLog } from "@/context/FoodLogContext";
import { useWeight } from "@/context/WeightContext";
import { useExercise } from "@/context/ExerciseContext";
import { useTheme } from "@/context/ThemeContext";
import { getShadowStyle } from "@/utils/shadowStyles";

export default function AnalyticsScreen() {
  const router = useRouter();
  const { resolvedTheme } = useTheme();
  const { history, clearHistory } = useHistory();
  const { profile } = useUserProfile();
  const { logs, getDailyNutrition } = useFoodLog();
  const {
    weightEntries,
    addWeightEntry,
    deleteWeightEntry,
    clearWeightHistory,
    getWeightTrend,
    calculateWeightChange,
  } = useWeight();
  const { exercises, clearExercisesForDay, calculateDailyCaloriesBurned } =
    useExercise();

  const [selectedTab, setSelectedTab] = useState<
    "food" | "weight" | "exercise"
  >("food");
  const [currentWeight, setCurrentWeight] = useState<string>("");
  const [isMetric, setIsMetric] = useState(true);

  // Animation values
  const fadeAnim = React.useRef(new Animated.Value(0)).current;
  const slideAnim = React.useRef(new Animated.Value(20)).current;

  // Calculate weight change percentage
  const weightChange = calculateWeightChange();

  // Get current weight for display
  const currentWeightValue =
    weightEntries.length > 0
      ? weightEntries[weightEntries.length - 1].weight
      : profile.weight;
  const weightUnit = isMetric ? "kg" : "lbs";
  const displayWeight = isMetric
    ? currentWeightValue.toFixed(1)
    : (currentWeightValue * 2.20462).toFixed(1);

  const displayTargetWeight = isMetric
    ? profile.targetWeight?.toFixed(1)
    : profile.targetWeight
    ? (profile.targetWeight * 2.20462).toFixed(1)
    : null;

  // Calculate BMI using weight in kg and height in cm
  const calculateBMI = () => {
    if (!profile.height || !profile.weight) return null;

    // Get the latest weight entry or use profile weight if no entries
    const currentWeightForBMI =
      weightEntries.length > 0
        ? weightEntries[weightEntries.length - 1].weight
        : profile.weight;

    // Convert height from cm to meters and square it
    const heightInMeters = profile.height / 100;
    const bmi = currentWeightForBMI / (heightInMeters * heightInMeters);

    return bmi;
  };

  // Determine BMI category
  const getBMICategory = (bmi: number) => {
    if (bmi < 18.6) return { category: "Underweight", color: "#3399FF" };
    if (bmi >= 18.6 && bmi < 24.9)
      return { category: "Healthy", color: "#4CAF50" };
    if (bmi >= 25 && bmi < 30)
      return { category: "Overweight", color: "#FFEB3B" };
    return { category: "Obese", color: "#FF5252" };
  };

  const animateContent = (toValue: number) => {
    const animations = [
      Animated.spring(fadeAnim, {
        toValue,
        tension: 50,
        friction: 7,
        useNativeDriver: true,
      }),
      Animated.spring(slideAnim, {
        toValue: toValue === 1 ? 0 : 20,
        tension: 50,
        friction: 7,
        useNativeDriver: true,
      }),
    ];

    Animated.parallel(animations).start();
  };

  useEffect(() => {
    animateContent(1);
  }, []);

  // Renamed from renderBMIComponent to renderWeightSummaryComponent
  const renderWeightSummaryComponent = () => {
    if (!profile.weight) return null; // Don't render if no profile weight

    return (
      <Animated.View
        style={{
          opacity: fadeAnim,
          transform: [{ translateY: slideAnim }],
        }}
        className="w-full mt-2" // Takes full width, add some top margin if needed after parent's text
      >
        {/* Current Weight and Percentage Change Row */}
        <View className="flex-row justify-between items-center">
          <View>
            <Text
              className={`text-sm mb-0.5 ${
                resolvedTheme === "light" ? "text-gray-500" : "text-gray-300"
              }`}
            >
              Current
            </Text>
            <Text
              className={`text-3xl font-bold ${
                resolvedTheme === "light" ? "text-gray-800" : "text-white"
              }`}
            >
              {displayWeight}{" "}
              <Text
                className={`text-xl font-semibold ${
                  resolvedTheme === "light" ? "text-gray-500" : "text-gray-300"
                }`}
              >
                {weightUnit}
              </Text>
            </Text>
          </View>

          {weightEntries.length > 1 && weightChange && (
            <View className="items-end">
              <Text
                className={`text-lg font-semibold ${
                  // Larger percentage text
                  weightChange.change > 0 ? "text-red-400" : "text-green-400"
                }`}
              >
                {weightChange.change > 0 ? "▲" : "▼"}{" "}
                {Math.abs(weightChange.percentage).toFixed(1)}%
              </Text>
              <Text className="text-xs text-gray-400 mt-0.5">Since last</Text>
            </View>
          )}
        </View>

        {/* Target Weight (Optional) */}
        {profile.goalType !== "maintain" && displayTargetWeight && (
          <View className="mt-3">
            {" "}
            {/* Adjusted margin */}
            <View className="flex-row justify-between items-center">
              <Text
                className={`text-sm ${
                  resolvedTheme === "light" ? "text-gray-500" : "text-gray-300"
                }`}
              >
                Target
              </Text>
              <Text
                className={`text-xl font-semibold ${
                  resolvedTheme === "light" ? "text-gray-800" : "text-white"
                }`}
              >
                {displayTargetWeight}{" "}
                <Text
                  className={`text-base font-medium ${
                    resolvedTheme === "light"
                      ? "text-gray-500"
                      : "text-gray-300"
                  }`}
                >
                  {weightUnit}
                </Text>
              </Text>
            </View>
            {/* Optional: Progress visualization could go here if desired */}
          </View>
        )}
      </Animated.View>
    );
  };

  // New component to render BMI information
  const renderBMICard = () => {
    const bmi = calculateBMI();
    if (bmi === null) return null;

    const roundedBMI = bmi.toFixed(2);
    const { category, color } = getBMICategory(bmi);

    // Calculate position for the indicator (0-100%)
    const indicatorPosition = Math.min(
      100,
      Math.max(
        0,
        bmi < 18.6
          ? (bmi / 18.6) * 25 // 0-25% for underweight
          : bmi < 24.9
          ? 25 + ((bmi - 18.6) / (24.9 - 18.6)) * 25 // 25-50% for healthy
          : bmi < 30
          ? 50 + ((bmi - 24.9) / (30 - 24.9)) * 25 // 50-75% for overweight
          : 75 + Math.min(25, ((bmi - 30) / 10) * 25) // 75-100% for obese (capped)
      )
    );

    return (
      <View
        className={`w-full mt-6 pt-6 border-t ${
          resolvedTheme === "light"
            ? "border-gray-200/60"
            : "border-gray-600/50"
        }`}
      >
        <View className="flex-row justify-between items-center mb-3">
          <Text
            className={`text-xl font-bold ${
              resolvedTheme === "light" ? "text-gray-800" : "text-white"
            }`}
          >
            Your BMI
          </Text>
          <Text
            style={{ color }}
            className="font-semibold text-sm px-3 py-1 rounded-full"
          >
            {category}
          </Text>
        </View>

        <Text
          className={`text-4xl font-bold mb-4 text-center ${
            resolvedTheme === "light" ? "text-gray-800" : "text-white"
          }`}
        >
          {roundedBMI}
        </Text>

        {/* BMI Slider */}
        <View className="mb-4">
          <View
            className={`h-2.5 w-full flex-row rounded-full overflow-hidden ${
              resolvedTheme === "light" ? "bg-gray-200/60" : "bg-gray-700/50"
            }`}
          >
            <View className="h-full bg-blue-500" style={{ width: "25%" }} />
            <View className="h-full bg-green-500" style={{ width: "25%" }} />
            <View className="h-full bg-yellow-400" style={{ width: "25%" }} />
            <View className="h-full bg-red-500" style={{ width: "25%" }} />
          </View>
          <View
            className={`h-4 w-4 rounded-full absolute -top-0.5 border-2 shadow-md ${
              resolvedTheme === "light"
                ? "bg-gray-800 border-gray-300"
                : "bg-white border-gray-700/80"
            }`}
            style={{ left: `${indicatorPosition}%`, marginLeft: -8 }}
          />
        </View>

        {/* Categories Text Labels */}
        <View className="flex-row justify-between text-xs">
          <Text
            className={`text-xs ${
              resolvedTheme === "light" ? "text-gray-500" : "text-gray-400"
            }`}
          >
            Underweight
          </Text>
          <Text
            className={`text-xs ${
              resolvedTheme === "light" ? "text-gray-500" : "text-gray-400"
            }`}
          >
            Healthy
          </Text>
          <Text
            className={`text-xs ${
              resolvedTheme === "light" ? "text-gray-500" : "text-gray-400"
            }`}
          >
            Overweight
          </Text>
          <Text
            className={`text-xs ${
              resolvedTheme === "light" ? "text-gray-500" : "text-gray-400"
            }`}
          >
            Obese
          </Text>
        </View>
      </View>
    );
  };

  const calculateTotalCalories = () => {
    return history.reduce(
      (total, item) => total + parseInt(item.info.calories),
      0
    );
  };

  const calculateAverageHealthScore = () => {
    if (history.length === 0) return 0;
    const total = history.reduce(
      (sum, item) => sum + (item.info.healthScore || 5),
      0
    );
    return Math.round(total / history.length);
  };

  // Get the past week's dates for nutritional trends
  const getPastWeekDates = () => {
    const dates = [];
    for (let i = 6; i >= 0; i--) {
      const date = new Date();
      date.setDate(date.getDate() - i);
      dates.push(date.toISOString().split("T")[0]);
    }
    return dates;
  };

  const pastWeekDates = getPastWeekDates();

  // Calculate average daily calories for the past week
  const getCaloriesTrend = () => {
    return pastWeekDates.map((date) => {
      const nutrition = getDailyNutrition(date);
      return {
        date,
        calories: nutrition.calories,
        // Format date for display
        displayDate: new Date(date).toLocaleDateString(undefined, {
          weekday: "short",
        }),
      };
    });
  };

  const caloriesTrend = getCaloriesTrend();
  const maxCalories = Math.max(
    ...caloriesTrend.map((entry) => entry.calories),
    profile.dailyCalorieGoal
  );

  // Calculate total calories burned across all tracked exercise
  const calculateTotalCaloriesBurned = () => {
    return Object.keys(exercises).reduce(
      (total, date) => total + calculateDailyCaloriesBurned(date),
      0
    );
  };

  // Computed values for analytics cards
  const totalFoodEntries = Array.isArray(logs) ? logs.length : 0;
  const totalWorkouts = Object.values(exercises).reduce(
    (total, dayExercises) => total + dayExercises.length,
    0
  );
  const totalCaloriesBurnedValue = calculateTotalCaloriesBurned();

  // Get the past 7 days for exercise tracking
  const getPastWeekExerciseData = () => {
    return pastWeekDates.map((date) => ({
      date,
      calories: calculateDailyCaloriesBurned(date),
      displayDate: new Date(date).toLocaleDateString(undefined, {
        weekday: "short",
      }),
    }));
  };

  const exerciseTrend = getPastWeekExerciseData();
  const maxExerciseCalories = Math.max(
    ...exerciseTrend.map((entry) => entry.calories),
    500
  ); // 500 as minimum scale

  const handleClearHistory = () => {
    Alert.alert(
      "Clear History",
      "Are you sure you want to clear all history?",
      [
        { text: "Cancel", style: "cancel" },
        { text: "Clear", style: "destructive", onPress: clearHistory },
      ]
    );
  };

  const handleAddWeightEntry = () => {
    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);

    if (!currentWeight || isNaN(parseFloat(currentWeight))) {
      Alert.alert("Invalid weight", "Please enter a valid weight");
      return;
    }

    addWeightEntry(parseFloat(currentWeight), isMetric)
      .then(() => {
        setCurrentWeight("");
      })
      .catch((error) => {
        Alert.alert("Error", "Failed to add weight entry. Please try again.");
      });
  };

  const handleDeleteWeightEntry = (date: string) => {
    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);

    Alert.alert(
      "Delete Weight Entry",
      "Are you sure you want to delete this weight entry?",
      [
        { text: "Cancel", style: "cancel" },
        {
          text: "Delete",
          style: "destructive",
          onPress: () => deleteWeightEntry(date),
        },
      ]
    );
  };

  const weightTrend = getWeightTrend(2);
  const weightStats = calculateWeightChange();

  const handleClearExerciseHistory = () => {
    Alert.alert(
      "Clear Exercise History",
      "Are you sure you want to clear all exercise history?",
      [
        { text: "Cancel", style: "cancel" },
        {
          text: "Clear",
          style: "destructive",
          onPress: async () => {
            try {
              // Clear each day's exercises
              for (const date of Object.keys(exercises)) {
                await clearExercisesForDay(date);
              }
            } catch (error) {
              console.error("Failed to clear exercise history:", error);
              Alert.alert(
                "Error",
                "Failed to clear exercise history. Please try again."
              );
            }
          },
        },
      ]
    );
  };

  const renderWeightTab = () => (
    <>
      <Animated.View
        style={{
          opacity: fadeAnim,
          transform: [{ translateY: slideAnim }],
        }}
        className="mb-6"
      >
        <View
          className={`rounded-3xl p-6 backdrop-blur-xl border ${
            resolvedTheme === "light"
              ? "bg-white border-gray-200/60"
              : "bg-gray-800 border-gray-700"
          }`}
          style={getShadowStyle(resolvedTheme, "card")}
        >
          <Text
            className={`text-xl font-bold mb-4 ${
              resolvedTheme === "light" ? "text-gray-800" : "text-white"
            }`}
          >
            Track Your Weight
          </Text>

          <View className="flex-row items-center space-x-2 mb-4">
            <TextInput
              className={`flex-1 px-4 py-3 rounded-xl ${
                resolvedTheme === "light"
                  ? "bg-gray-100/60 text-gray-800"
                  : "bg-gray-700/50 text-white"
              }`}
              placeholderTextColor={
                resolvedTheme === "light" ? "#6b7280" : "#888"
              }
              keyboardType="numeric"
              value={currentWeight}
              onChangeText={setCurrentWeight}
              placeholder={`Weight in ${isMetric ? "kg" : "lbs"}`}
            />
            <TouchableOpacity
              onPress={() => setIsMetric(!isMetric)}
              className={`px-4 py-3 rounded-xl ${
                resolvedTheme === "light" ? "bg-gray-100/60" : "bg-gray-700/50"
              }`}
            >
              <Text
                className={`font-semibold ${
                  resolvedTheme === "light" ? "text-gray-800" : "text-white"
                }`}
              >
                {isMetric ? "kg" : "lbs"}
              </Text>
            </TouchableOpacity>
            <TouchableOpacity
              onPress={handleAddWeightEntry}
              className="bg-gradient-to-r from-green-500 to-emerald-600 px-4 py-3 rounded-xl"
            >
              <Text className="text-white font-semibold">Save</Text>
            </TouchableOpacity>
          </View>

          {weightEntries.length > 0 && (
            <View
              className={`mt-4 p-4 rounded-xl ${
                resolvedTheme === "light" ? "bg-gray-100/60" : "bg-gray-700/50"
              }`}
            >
              <Text
                className={`font-semibold mb-2 ${
                  resolvedTheme === "light" ? "text-gray-800" : "text-white"
                }`}
              >
                {weightStats.period} Weight Change
              </Text>
              <View className="flex-row justify-between mb-2">
                <Text
                  className={`${
                    resolvedTheme === "light"
                      ? "text-gray-500"
                      : "text-gray-400"
                  }`}
                >
                  {weightStats.change > 0 ? "Gained" : "Lost"}
                </Text>
                <Text
                  className={`font-bold ${
                    weightStats.change > 0 && profile.goalType === "lose"
                      ? "text-red-500"
                      : weightStats.change < 0 && profile.goalType === "gain"
                      ? "text-red-500"
                      : "text-green-500"
                  }`}
                >
                  {Math.abs(weightStats.change).toFixed(1)}{" "}
                  {isMetric ? "kg" : "lbs"} ({weightStats.percentage.toFixed(1)}
                  %)
                </Text>
              </View>

              {profile.targetWeight && profile.goalType !== "maintain" && (
                <View className="mt-2">
                  <Text
                    className={`mb-1 ${
                      resolvedTheme === "light"
                        ? "text-gray-500"
                        : "text-gray-400"
                    }`}
                  >
                    Progress to Goal
                  </Text>
                  <View
                    className={`h-2 rounded-full overflow-hidden ${
                      resolvedTheme === "light"
                        ? "bg-gray-200/60"
                        : "bg-gray-700/50"
                    }`}
                  >
                    <View
                      className="h-full bg-gradient-to-r from-blue-500 to-blue-600 rounded-full"
                      style={{
                        width: `${Math.min(
                          100,
                          Math.max(
                            0,
                            profile.goalType === "lose"
                              ? ((profile.weight -
                                  weightEntries[weightEntries.length - 1]
                                    .weight) /
                                  (profile.weight - profile.targetWeight)) *
                                  100
                              : ((weightEntries[weightEntries.length - 1]
                                  .weight -
                                  profile.weight) /
                                  (profile.targetWeight - profile.weight)) *
                                  100
                          )
                        )}%`,
                      }}
                    />
                  </View>
                </View>
              )}
            </View>
          )}
        </View>
      </Animated.View>

      {/* Weight trend visualization */}
      {weightTrend.length > 0 &&
        (() => {
          const allWeights = weightTrend.map((e) => e.weight);
          const minWeight = Math.min(...allWeights);
          const maxWeight = Math.max(...allWeights);
          const range = maxWeight - minWeight || 1;

          return (
            <Animated.View
              style={{
                opacity: fadeAnim,
                transform: [{ translateY: slideAnim }],
                ...getShadowStyle(resolvedTheme, "card"),
              }}
              className={`rounded-3xl p-6 mb-6 backdrop-blur-xl border ${
                resolvedTheme === "light"
                  ? "bg-white border-gray-200/60"
                  : "bg-gray-800 border-gray-700"
              }`}
            >
              <Text
                className={`text-xl font-bold mb-2 ${
                  resolvedTheme === "light" ? "text-gray-800" : "text-white"
                }`}
              >
                Weight Trend
              </Text>
              <Text
                className={`text-sm mb-4 ${
                  resolvedTheme === "light" ? "text-gray-500" : "text-gray-400"
                }`}
              >
                Tracking your weight journey
              </Text>

              <View className="h-48 flex-row items-end justify-between">
                {weightTrend.map((entry, index) => {
                  const normalizedHeight =
                    ((entry.weight - minWeight) / range) * 75 + 15;

                  // Determine if this entry shows weight gain or loss compared to previous entry
                  const isWeightLoss =
                    index > 0 && entry.weight < weightTrend[index - 1].weight;
                  const isWeightGain =
                    index > 0 && entry.weight > weightTrend[index - 1].weight;

                  // Check if this matches the user's goal
                  const matchesGoal =
                    (profile.goalType === "lose" && isWeightLoss) ||
                    (profile.goalType === "gain" && isWeightGain) ||
                    (profile.goalType === "maintain" &&
                      !isWeightLoss &&
                      !isWeightGain);

                  // Determine gradient colors based on goal match
                  const gradientColors = !index
                    ? (["#64B5F6", "#2196F3"] as const) // First entry (blue)
                    : matchesGoal
                    ? (["#4CAF50", "#2E7D32"] as const) // Goal matched (green)
                    : (["#FF5252", "#D32F2F"] as const); // Goal not matched (red)

                  return (
                    <TouchableOpacity
                      key={entry.date}
                      onLongPress={() => handleDeleteWeightEntry(entry.date)}
                      className="items-center flex-1"
                    >
                      {/* The bar with gradient */}
                      <View className="w-8 overflow-hidden rounded-t-lg">
                        <LinearGradient
                          colors={gradientColors}
                          className="rounded-t-lg w-full"
                          style={{ height: normalizedHeight }}
                        />
                      </View>

                      {/* Date */}
                      <Text
                        className={`text-xs mt-2 text-center font-medium ${
                          resolvedTheme === "light"
                            ? "text-gray-600"
                            : "text-gray-300"
                        }`}
                      >
                        {new Date(entry.date).toLocaleDateString(undefined, {
                          month: "short",
                          day: "numeric",
                        })}
                      </Text>

                      {/* Weight value with indicator */}
                      <View className="flex-row items-center mt-1">
                        {index > 0 && (
                          <Ionicons
                            name={
                              isWeightLoss
                                ? "arrow-down"
                                : isWeightGain
                                ? "arrow-up"
                                : "remove"
                            }
                            size={12}
                            color={
                              !isWeightLoss && !isWeightGain
                                ? "#64B5F6"
                                : matchesGoal
                                ? "#4CAF50"
                                : "#FF5252"
                            }
                            style={{ marginRight: 1 }}
                          />
                        )}
                        <Text
                          className={`text-xs font-medium ${
                            !index
                              ? "text-blue-300"
                              : matchesGoal
                              ? "text-green-400"
                              : "text-red-400"
                          }`}
                        >
                          {isMetric
                            ? entry.weight.toFixed(1)
                            : (entry.weight / 0.453592).toFixed(1)}
                        </Text>
                      </View>

                      {/* Show difference from previous */}
                      {index > 0 &&
                        Math.abs(entry.weight - weightTrend[index - 1].weight) >
                          0.05 && (
                          <Text className="text-xs text-gray-400">
                            {isWeightLoss ? "-" : "+"}
                            {Math.abs(
                              entry.weight - weightTrend[index - 1].weight
                            ).toFixed(1)}
                          </Text>
                        )}
                    </TouchableOpacity>
                  );
                })}
              </View>

              {/* Target indicator */}
              {profile.targetWeight && (
                <View
                  className={`flex-row justify-center mt-4 p-3 rounded-xl ${
                    resolvedTheme === "light"
                      ? "bg-gray-100/60"
                      : "bg-gray-700/50"
                  }`}
                >
                  <View className="flex-row items-center">
                    <Ionicons
                      name="flag"
                      size={18}
                      color="#FFD700"
                      style={{ marginRight: 6 }}
                    />
                    <Text
                      className={`font-medium ${
                        resolvedTheme === "light"
                          ? "text-gray-800"
                          : "text-white"
                      }`}
                    >
                      Target:{" "}
                      <Text className="text-yellow-600">
                        {isMetric
                          ? profile.targetWeight.toFixed(1) + " kg"
                          : (profile.targetWeight / 0.453592).toFixed(1) +
                            " lbs"}
                      </Text>
                    </Text>
                  </View>
                </View>
              )}
            </Animated.View>
          );
        })()}

      {/* Weight history list */}
      <Animated.View
        style={{
          opacity: fadeAnim,
          transform: [{ translateY: slideAnim }],
          ...getShadowStyle(resolvedTheme, "card"),
        }}
        className={`rounded-3xl p-6 mb-6 backdrop-blur-xl border ${
          resolvedTheme === "light"
            ? "bg-white border-gray-200/60"
            : "bg-gray-800 border-gray-700"
        }`}
      >
        <View className="flex-row justify-between items-center mb-4">
          <Text
            className={`text-xl font-bold ${
              resolvedTheme === "light" ? "text-gray-800" : "text-white"
            }`}
          >
            Weight History
          </Text>
          {weightEntries.length > 0 && (
            <TouchableOpacity
              onPress={() => {
                Alert.alert(
                  "Clear Weight History",
                  "Are you sure you want to clear all weight entries?",
                  [
                    { text: "Cancel", style: "cancel" },
                    {
                      text: "Clear",
                      style: "destructive",
                      onPress: clearWeightHistory,
                    },
                  ]
                );
              }}
              className="bg-red-500/20 px-3 py-1 rounded-full"
            >
              <Text className="text-red-500 font-semibold text-sm">Clear</Text>
            </TouchableOpacity>
          )}
        </View>

        {weightEntries.length === 0 ? (
          <View className="items-center justify-center py-8">
            <Ionicons
              name="scale-outline"
              size={64}
              color={resolvedTheme === "light" ? "#9ca3af" : "#666"}
            />
            <Text
              className={`text-center mt-4 ${
                resolvedTheme === "light" ? "text-gray-500" : "text-gray-400"
              }`}
            >
              No weight entries yet. Start tracking your progress!
            </Text>
          </View>
        ) : (
          weightEntries.map((entry, index) => (
            <View
              key={entry.date}
              className={`flex-row justify-between items-center py-3 border-b ${
                resolvedTheme === "light"
                  ? "border-gray-200/60"
                  : "border-gray-600/50"
              }`}
            >
              <Text
                className={`${
                  resolvedTheme === "light" ? "text-gray-800" : "text-white"
                }`}
              >
                {new Date(entry.date).toLocaleDateString()}
              </Text>
              <View className="flex-row items-center">
                <Text
                  className={`font-semibold mr-3 ${
                    resolvedTheme === "light" ? "text-gray-800" : "text-white"
                  }`}
                >
                  {isMetric
                    ? `${entry.weight.toFixed(1)} kg`
                    : `${(entry.weight / 0.453592).toFixed(1)} lbs`}
                </Text>
                <TouchableOpacity
                  onPress={() => handleDeleteWeightEntry(entry.date)}
                  className="p-2"
                >
                  <Ionicons name="close-circle" size={20} color="#FF6B6B" />
                </TouchableOpacity>
              </View>
            </View>
          ))
        )}
      </Animated.View>
    </>
  );

  const renderFoodTab = () => (
    <>
      <View className="flex-row justify-between items-center mb-6">
        <Animated.View
          style={{
            opacity: fadeAnim,
            transform: [{ translateY: slideAnim }],
            ...getShadowStyle(resolvedTheme, "card"),
          }}
          className={`rounded-xl p-4 flex-1 mr-2 backdrop-blur-xl border ${
            resolvedTheme === "light"
              ? "bg-white border-gray-200/60"
              : "bg-gray-800 border-gray-700"
          }`}
        >
          <Text
            className={`text-sm ${
              resolvedTheme === "light" ? "text-gray-500" : "text-gray-400"
            }`}
          >
            Total Calories
          </Text>
          <Text
            className={`text-2xl font-bold ${
              resolvedTheme === "light" ? "text-gray-800" : "text-white"
            }`}
          >
            {calculateTotalCalories()}
          </Text>
        </Animated.View>
        <Animated.View
          style={{
            opacity: fadeAnim,
            transform: [{ translateY: slideAnim }],
            ...getShadowStyle(resolvedTheme, "card"),
          }}
          className={`rounded-xl p-4 flex-1 ml-2 backdrop-blur-xl border ${
            resolvedTheme === "light"
              ? "bg-white border-gray-200/60"
              : "bg-gray-800 border-gray-700"
          }`}
        >
          <Text
            className={`text-sm ${
              resolvedTheme === "light" ? "text-gray-500" : "text-gray-400"
            }`}
          >
            Avg. Health Score
          </Text>
          <Text
            className={`text-2xl font-bold ${
              resolvedTheme === "light" ? "text-gray-800" : "text-white"
            }`}
          >
            {calculateAverageHealthScore()}/10
          </Text>
        </Animated.View>
      </View>

      {/* Weekly calorie trend */}
      <Animated.View
        style={{
          opacity: fadeAnim,
          transform: [{ translateY: slideAnim }],
          ...getShadowStyle(resolvedTheme, "card"),
        }}
        className={`rounded-3xl p-6 mb-6 backdrop-blur-xl border ${
          resolvedTheme === "light"
            ? "bg-white border-gray-200/60"
            : "bg-gray-800 border-gray-700"
        }`}
      >
        <Text
          className={`text-xl font-bold mb-2 ${
            resolvedTheme === "light" ? "text-gray-800" : "text-white"
          }`}
        >
          Weekly Calories
        </Text>
        <Text
          className={`text-sm mb-4 ${
            resolvedTheme === "light" ? "text-gray-500" : "text-gray-400"
          }`}
        >
          Your past 7 days of intake vs. goal
        </Text>

        <View className="h-48 flex-row items-end justify-between mt-2">
          {caloriesTrend.map((day, index) => {
            // Calculate bar height based on calories (max height 100)
            // Cap the height at 80% to prevent text overlap
            const barHeight = maxCalories
              ? Math.min((day.calories / maxCalories) * 100, 80)
              : 0;

            // Whether this day is over or under goal
            const isOverGoal = day.calories > profile.dailyCalorieGoal;

            // Calculate goal line position
            const goalLinePosition =
              (profile.dailyCalorieGoal / maxCalories) * 100;

            return (
              <View key={day.date} className="items-center flex-1">
                {/* Day marker line */}
                <View
                  className={`h-0.5 w-full absolute ${
                    resolvedTheme === "light"
                      ? "bg-gray-300/50"
                      : "bg-gray-600/50"
                  }`}
                  style={{ bottom: `${goalLinePosition}%` }}
                />

                {/* The actual bar with gradient */}
                <View className="w-8 overflow-hidden rounded-t-lg">
                  <LinearGradient
                    colors={
                      isOverGoal
                        ? (["#FF9800", "#FF5722"] as const) // Orange to red for over goal
                        : (["#4CAF50", "#8BC34A"] as const) // Green gradient for under goal
                    }
                    className="rounded-t-lg w-full"
                    style={{ height: `${Math.max(5, barHeight)}%` }}
                  />
                </View>

                {/* Day name */}
                <Text
                  className={`text-xs mt-2 font-medium ${
                    resolvedTheme === "light"
                      ? "text-gray-600"
                      : "text-gray-300"
                  }`}
                >
                  {day.displayDate}
                </Text>

                {/* Calorie value with indicator icon */}
                <View className="flex-row items-center mt-1">
                  {day.calories > 0 && (
                    <Ionicons
                      name={isOverGoal ? "arrow-up" : "arrow-down"}
                      size={12}
                      color={isOverGoal ? "#FF5722" : "#4CAF50"}
                      style={{ marginRight: 2 }}
                    />
                  )}
                  <Text
                    className={`text-xs ${
                      isOverGoal ? "text-orange-400" : "text-green-400"
                    }`}
                  >
                    {day.calories > 0 ? day.calories : "-"}
                  </Text>
                </View>
              </View>
            );
          })}
        </View>

        {/* Goal indicator */}
        <View
          className={`flex-row justify-center mt-4 p-3 rounded-xl ${
            resolvedTheme === "light" ? "bg-gray-100/60" : "bg-gray-700/50"
          }`}
        >
          <View className="flex-row items-center">
            <Ionicons
              name="trophy"
              size={18}
              color="#FFD700"
              style={{ marginRight: 6 }}
            />
            <Text
              className={`font-medium ${
                resolvedTheme === "light" ? "text-gray-800" : "text-white"
              }`}
            >
              Daily Goal:{" "}
              <Text className="text-yellow-600">
                {profile.dailyCalorieGoal} kcal
              </Text>
            </Text>
          </View>
        </View>
      </Animated.View>
    </>
  );

  const renderExerciseTab = () => {
    const hasExercises = Object.keys(exercises).length > 0;
    const totalCaloriesBurned = calculateTotalCaloriesBurned();

    return (
      <>
        <View className="flex-row justify-between items-center mb-6">
          <Animated.View
            style={{
              opacity: fadeAnim,
              transform: [{ translateY: slideAnim }],
              ...getShadowStyle(resolvedTheme, "card"),
            }}
            className={`rounded-xl p-4 flex-1 backdrop-blur-xl border ${
              resolvedTheme === "light"
                ? "bg-white border-gray-200/60"
                : "bg-gray-800 border-gray-700"
            }`}
          >
            <Text
              className={`text-sm ${
                resolvedTheme === "light" ? "text-gray-500" : "text-gray-400"
              }`}
            >
              Total Calories Burned
            </Text>
            <Text
              className={`text-2xl font-bold ${
                resolvedTheme === "light" ? "text-gray-800" : "text-white"
              }`}
            >
              {totalCaloriesBurned}
            </Text>
          </Animated.View>
        </View>

        {/* Weekly exercise trend */}
        <Animated.View
          style={{
            opacity: fadeAnim,
            transform: [{ translateY: slideAnim }],
            ...getShadowStyle(resolvedTheme, "card"),
          }}
          className={`rounded-3xl p-6 mb-6 backdrop-blur-xl border ${
            resolvedTheme === "light"
              ? "bg-white border-gray-200/60"
              : "bg-gray-800 border-gray-700"
          }`}
        >
          <Text
            className={`text-xl font-bold mb-2 ${
              resolvedTheme === "light" ? "text-gray-800" : "text-white"
            }`}
          >
            Weekly Exercise
          </Text>
          <Text
            className={`text-sm mb-4 ${
              resolvedTheme === "light" ? "text-gray-500" : "text-gray-400"
            }`}
          >
            Calories burned in the past 7 days
          </Text>

          <View className="h-48 flex-row items-end justify-between mt-2">
            {exerciseTrend.map((day, index) => {
              // Calculate bar height based on calories (max height 100)
              // Cap the height at 80% to prevent text overlap
              const barHeight = maxExerciseCalories
                ? Math.min((day.calories / maxExerciseCalories) * 100, 80)
                : 0;

              // Find the max day to highlight
              const isMaxDay =
                day.calories ===
                Math.max(...exerciseTrend.map((d) => d.calories));

              return (
                <View key={day.date} className="items-center flex-1">
                  {/* The actual bar with gradient */}
                  <View className="w-8 overflow-hidden rounded-t-lg">
                    <LinearGradient
                      colors={
                        isMaxDay
                          ? (["#9C27B0", "#673AB7"] as const) // Purple gradient for max day
                          : (["#7C4DFF", "#3F51B5"] as const) // Blue gradient for other days
                      }
                      className="rounded-t-lg w-full"
                      style={{ height: `${Math.max(5, barHeight)}%` }}
                    />
                  </View>

                  {/* Day name */}
                  <Text
                    className={`text-xs mt-2 font-medium ${
                      resolvedTheme === "light"
                        ? "text-gray-600"
                        : "text-gray-300"
                    }`}
                  >
                    {day.displayDate}
                  </Text>

                  {/* Calorie value with fire icon */}
                  <View className="flex-row items-center mt-1">
                    {day.calories > 0 && (
                      <Ionicons
                        name="flame"
                        size={12}
                        color="#FF5722"
                        style={{ marginRight: 2 }}
                      />
                    )}
                    <Text
                      className={`text-xs ${
                        isMaxDay
                          ? "text-purple-300 font-bold"
                          : "text-indigo-300"
                      }`}
                    >
                      {day.calories > 0 ? day.calories : "-"}
                    </Text>
                  </View>
                </View>
              );
            })}
          </View>

          {/* Average indicator */}
          <View
            className={`flex-row justify-center mt-4 p-3 rounded-xl ${
              resolvedTheme === "light" ? "bg-gray-100/60" : "bg-gray-700/50"
            }`}
          >
            <View className="flex-row items-center">
              <Ionicons
                name="fitness"
                size={18}
                color="#7C4DFF"
                style={{ marginRight: 6 }}
              />
              <Text
                className={`font-medium ${
                  resolvedTheme === "light" ? "text-gray-800" : "text-white"
                }`}
              >
                Avg. Daily:{" "}
                <Text className="text-purple-600">
                  {Math.round(
                    totalCaloriesBurnedValue /
                      Math.max(1, Object.keys(exercises).length)
                  )}{" "}
                  kcal
                </Text>
              </Text>
            </View>
          </View>
        </Animated.View>
      </>
    );
  };

  return (
    <View
      className={`flex-1 ${
        resolvedTheme === "light" ? "bg-gray-50" : "bg-gray-950"
      }`}
    >
      <LinearGradient
        colors={
          resolvedTheme === "light"
            ? ["#f8fafc", "#e2e8f0", "#f1f5f9"]
            : ["#111827", "#1F2937"]
        }
        className="flex-1"
      >
        <ScrollView className="flex-1 p-5" scrollEventThrottle={16}>
          <Animated.View
            style={{
              opacity: fadeAnim,
              transform: [{ translateY: slideAnim }],
              ...getShadowStyle(resolvedTheme, "card"),
            }}
            className={`items-center mb-8 rounded-3xl p-6 backdrop-blur-xl border ${
              resolvedTheme === "light"
                ? "bg-white border-gray-200/60"
                : "bg-gray-800 border-gray-700"
            }`}
          >
            <Text
              className={`text-4xl font-bold mb-2 ${
                resolvedTheme === "light" ? "text-gray-800" : "text-white"
              }`}
            >
              Analytics
            </Text>
            <Text
              className={`text-center text-lg mb-6 ${
                resolvedTheme === "light" ? "text-gray-600" : "text-gray-300"
              }`}
            >
              Track your progress and insights
            </Text>

            {/* Weight Summary Display - now more integrated */}
            {renderWeightSummaryComponent()}
            {/* BMI Display - now integrated into the main header */}
            {renderBMICard()}
          </Animated.View>

          {/* Tab Selector */}
          <View
            className={`flex-row mb-6 rounded-full p-1 backdrop-blur-xl border ${
              resolvedTheme === "light"
                ? "bg-white border-gray-200/60"
                : "bg-gray-800 border-gray-700"
            }`}
            style={getShadowStyle(resolvedTheme, "card")}
          >
            <TouchableOpacity
              className={`flex-1 py-2 rounded-full ${
                selectedTab === "food"
                  ? resolvedTheme === "light"
                    ? "bg-gray-200/60"
                    : "bg-white/20"
                  : ""
              }`}
              onPress={() => {
                Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
                setSelectedTab("food");
              }}
            >
              <Text
                className={`text-center ${
                  selectedTab === "food"
                    ? resolvedTheme === "light"
                      ? "text-gray-800 font-semibold"
                      : "text-white font-semibold"
                    : resolvedTheme === "light"
                    ? "text-gray-500"
                    : "text-gray-400"
                }`}
              >
                Food
              </Text>
            </TouchableOpacity>
            <TouchableOpacity
              className={`flex-1 py-2 rounded-full ${
                selectedTab === "exercise"
                  ? resolvedTheme === "light"
                    ? "bg-gray-200/60"
                    : "bg-white/20"
                  : ""
              }`}
              onPress={() => {
                Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
                setSelectedTab("exercise");
              }}
            >
              <Text
                className={`text-center ${
                  selectedTab === "exercise"
                    ? resolvedTheme === "light"
                      ? "text-gray-800 font-semibold"
                      : "text-white font-semibold"
                    : resolvedTheme === "light"
                    ? "text-gray-500"
                    : "text-gray-400"
                }`}
              >
                Exercise
              </Text>
            </TouchableOpacity>
            <TouchableOpacity
              className={`flex-1 py-2 rounded-full ${
                selectedTab === "weight"
                  ? resolvedTheme === "light"
                    ? "bg-gray-200/60"
                    : "bg-white/20"
                  : ""
              }`}
              onPress={() => {
                Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
                setSelectedTab("weight");
              }}
            >
              <Text
                className={`text-center ${
                  selectedTab === "weight"
                    ? resolvedTheme === "light"
                      ? "text-gray-800 font-semibold"
                      : "text-white font-semibold"
                    : resolvedTheme === "light"
                    ? "text-gray-500"
                    : "text-gray-400"
                }`}
              >
                Weight
              </Text>
            </TouchableOpacity>
          </View>

          {selectedTab === "food"
            ? renderFoodTab()
            : selectedTab === "exercise"
            ? renderExerciseTab()
            : renderWeightTab()}
        </ScrollView>
      </LinearGradient>
    </View>
  );
}
