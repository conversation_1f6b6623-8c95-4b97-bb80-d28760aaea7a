import { supabase, getCurrentUserId } from "../config/supabase";
import { Exercise } from "../context/ExerciseContext";

interface SupabaseExercise {
  id: string;
  user_id: string;
  name: string;
  date: string; // Now a timestamptz in the database
  duration: number;
  calories_burned: number;
  exercise_type: string;
  created_at: string;
}

// Function to format date for timestamptz queries
const formatDateToTimestampRange = (dateString: string) => {
  // Convert date string (YYYY-MM-DD) to timestamp range for that day
  const startDate = new Date(`${dateString}T00:00:00.000Z`);
  const endDate = new Date(`${dateString}T23:59:59.999Z`);
  return { startDate, endDate };
};

// Convert from app format to database format
const transformExerciseForSupabase = async (
  exercise: Omit<Exercise, "id">
): Promise<Omit<SupabaseExercise, "id" | "created_at">> => {
  const userId = await getCurrentUserId();

  if (!userId) {
    throw new Error(
      "User ID not available. Please ensure you are authenticated."
    );
  }

  // Format date string to ISO format with time
  const dateWithTime = new Date(`${exercise.date}T12:00:00.000Z`).toISOString();

  return {
    user_id: userId,
    name: exercise.name,
    date: dateWithTime, // Use the formatted timestamp
    duration: exercise.duration,
    calories_burned: exercise.caloriesBurned,
    exercise_type: exercise.type,
  };
};

// Convert from database format to app format
const transformExerciseFromSupabase = (
  exercise: SupabaseExercise
): Exercise => {
  // Extract only the date portion (YYYY-MM-DD) from the timestamp
  const dateOnly = new Date(exercise.date).toISOString().split("T")[0];

  return {
    id: exercise.id,
    name: exercise.name,
    date: dateOnly, // Store only the date part
    duration: exercise.duration,
    caloriesBurned: exercise.calories_burned,
    type: exercise.exercise_type as any,
    createdAt: exercise.created_at,
  };
};

// Get exercise logs for a specific date
export const getExercisesByDate = async (date: string): Promise<Exercise[]> => {
  try {
    const userId = await getCurrentUserId();

    if (!userId) {
      console.error(
        "User ID not available. Please ensure you are authenticated."
      );
      return [];
    }

    // Get timestamp range for the specific date
    const { startDate, endDate } = formatDateToTimestampRange(date);

    const { data, error } = await supabase
      .from("exercise_logs")
      .select("*")
      .eq("user_id", userId)
      .gte("date", startDate.toISOString())
      .lte("date", endDate.toISOString())
      .order("date", { ascending: true });

    if (error) {
      console.error("Error fetching exercise logs:", error);
      return [];
    }

    return data?.map(transformExerciseFromSupabase) || [];
  } catch (error) {
    console.error("Error in getExercisesByDate:", error);
    return [];
  }
};

// Get all exercise logs
export const getAllExercises = async (): Promise<
  Record<string, Exercise[]>
> => {
  try {
    const userId = await getCurrentUserId();

    if (!userId) {
      console.error(
        "User ID not available. Please ensure you are authenticated."
      );
      return {};
    }

    const { data, error } = await supabase
      .from("exercise_logs")
      .select("*")
      .eq("user_id", userId)
      .order("date", { ascending: true });

    if (error) {
      console.error("Error fetching all exercise logs:", error);
      return {};
    }

    const exerciseLogs = data?.map(transformExerciseFromSupabase) || [];

    // Group by date
    return exerciseLogs.reduce((acc, item) => {
      if (!acc[item.date]) {
        acc[item.date] = [];
      }
      acc[item.date].push(item);
      return acc;
    }, {} as Record<string, Exercise[]>);
  } catch (error) {
    console.error("Error in getAllExercises:", error);
    return {};
  }
};

// Add a new exercise
export const addExercise = async (
  exercise: Omit<Exercise, "id">
): Promise<Exercise | null> => {
  try {
    const supabaseExercise = await transformExerciseForSupabase(exercise);

    const { data, error } = await supabase
      .from("exercise_logs")
      .insert(supabaseExercise)
      .select()
      .maybeSingle();

    if (error) {
      console.error("Error adding exercise:", error);
      return null;
    }

    if (!data) {
      console.error("No data returned after inserting exercise");
      return null;
    }

    return transformExerciseFromSupabase(data);
  } catch (error) {
    console.error("Error in addExercise:", error);
    return null;
  }
};

// Update an existing exercise
export const updateExercise = async (exercise: Exercise): Promise<boolean> => {
  try {
    const userId = await getCurrentUserId();

    if (!userId) {
      console.error(
        "User ID not available. Please ensure you are authenticated."
      );
      return false;
    }

    const supabaseExercise = await transformExerciseForSupabase(exercise);

    const { error } = await supabase
      .from("exercise_logs")
      .update(supabaseExercise)
      .eq("id", exercise.id)
      .eq("user_id", userId);

    if (error) {
      console.error("Error updating exercise:", error);
      return false;
    }

    return true;
  } catch (error) {
    console.error("Error in updateExercise:", error);
    return false;
  }
};

// Delete an exercise
export const deleteExercise = async (id: string): Promise<boolean> => {
  try {
    const userId = await getCurrentUserId();

    if (!userId) {
      console.error(
        "User ID not available. Please ensure you are authenticated."
      );
      return false;
    }

    const { error } = await supabase
      .from("exercise_logs")
      .delete()
      .eq("id", id)
      .eq("user_id", userId);

    if (error) {
      console.error("Error deleting exercise:", error);
      return false;
    }

    return true;
  } catch (error) {
    console.error("Error in deleteExercise:", error);
    return false;
  }
};

// Clear all exercise logs for a specific date
export const clearExercisesForDay = async (date: string): Promise<boolean> => {
  try {
    const userId = await getCurrentUserId();

    if (!userId) {
      console.error(
        "User ID not available. Please ensure you are authenticated."
      );
      return false;
    }

    // Get timestamp range for the specific date
    const { startDate, endDate } = formatDateToTimestampRange(date);

    const { error } = await supabase
      .from("exercise_logs")
      .delete()
      .eq("user_id", userId)
      .gte("date", startDate.toISOString())
      .lte("date", endDate.toISOString());

    if (error) {
      console.error("Error clearing exercise logs for day:", error);
      return false;
    }

    return true;
  } catch (error) {
    console.error("Error in clearExercisesForDay:", error);
    return false;
  }
};
