import React, { useState, useEffect } from "react";
import {
  View,
  Image,
  TouchableOpacity,
  ActivityIndicator,
  Text,
  StyleSheet,
  Alert,
} from "react-native";
import * as ImagePicker from "expo-image-picker";
import {
  uploadImage,
  deleteImage,
  getPathFromUrl,
  getImageUrl,
} from "../services/ImageService";
import { MaterialIcons } from "@expo/vector-icons";

interface ImageHelperProps {
  imageUri: string | null;
  onImageChange: (newUri: string | null) => void;
  size?: number;
  allowDelete?: boolean;
  placeholder?: React.ReactNode;
}

/**
 * A reusable component for handling image selection, uploading, and display
 */
export const ImageHelper: React.FC<ImageHelperProps> = ({
  imageUri,
  onImageChange,
  size = 150,
  allowDelete = true,
  placeholder,
}) => {
  const [isUploading, setIsUploading] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [displayUri, setDisplayUri] = useState<string | null>(null);

  // When imageUri changes, get a signed URL if needed
  useEffect(() => {
    const loadImage = async () => {
      if (!imageUri) {
        setDisplayUri(null);
        return;
      }

      setIsLoading(true);
      setError(null);

      try {
        // If it's already a file:// or http:// URI, use it directly
        if (imageUri.startsWith("file:") || imageUri.startsWith("http")) {
          console.log("Using direct URI:", imageUri);
          setDisplayUri(imageUri);
          return;
        }

        // Otherwise get a signed URL for the path
        console.log("Getting signed URL for:", imageUri);
        const url = await getImageUrl(imageUri);

        if (!url) {
          throw new Error("Failed to get image URL");
        }

        console.log("Received display URL:", url);
        setDisplayUri(url);
      } catch (err) {
        console.error("Error getting image URL:", err);
        setError("Failed to load image");
      } finally {
        setIsLoading(false);
      }
    };

    loadImage();
  }, [imageUri]);

  // Request camera roll permissions
  const requestPermissions = async () => {
    const { status } = await ImagePicker.requestMediaLibraryPermissionsAsync();
    if (status !== "granted") {
      setError("Permission to access media library is required!");
      return false;
    }
    return true;
  };

  // Pick an image from gallery
  const pickImage = async () => {
    setError(null);

    if (!(await requestPermissions())) return;

    try {
      const result = await ImagePicker.launchImageLibraryAsync({
        mediaTypes: ImagePicker.MediaTypeOptions.Images,
        allowsEditing: true,
        aspect: [1, 1],
        quality: 0.7, // Reduced quality for smaller file size
      });

      if (!result.canceled && result.assets && result.assets[0]) {
        const selectedImageUri = result.assets[0].uri;
        handleImageUpload(selectedImageUri);
      }
    } catch (err) {
      console.error("Error picking image:", err);
      setError("Failed to pick image");
    }
  };

  // Handle the image upload process
  const handleImageUpload = async (localUri: string) => {
    setIsUploading(true);
    setError(null);

    try {
      // Upload to Supabase storage and get path
      console.log("Starting upload of:", localUri);
      const uploadedPath = await uploadImage(localUri);

      if (uploadedPath) {
        console.log("Upload successful, path:", uploadedPath);
        // Pass the image path back to parent component
        onImageChange(uploadedPath);
      } else {
        Alert.alert(
          "Upload Failed",
          "Could not upload the image to storage. Would you like to try again?",
          [
            { text: "Cancel", style: "cancel" },
            { text: "Try Again", onPress: () => handleImageUpload(localUri) },
          ]
        );
        setError("Upload failed - check connection");
      }
    } catch (err) {
      console.error("Error uploading image:", err);
      setError("Upload failed");
    } finally {
      setIsUploading(false);
    }
  };

  // Delete the current image
  const handleDeleteImage = async () => {
    if (!imageUri) return;

    setIsUploading(true);
    setError(null);

    try {
      // If it's already a path (not a URL), delete it directly
      if (!imageUri.startsWith("http") && !imageUri.startsWith("file:")) {
        await deleteImage(imageUri);
      }
      // If it's a Supabase URL, extract the path and delete it
      else if (imageUri.includes("supabase")) {
        const path = getPathFromUrl(imageUri);
        if (path) {
          await deleteImage(path);
        }
      }

      // Update state regardless of whether the delete was successful
      onImageChange(null);
    } catch (err) {
      console.error("Error deleting image:", err);
      setError("Failed to delete image");
    } finally {
      setIsUploading(false);
    }
  };

  return (
    <View style={[styles.container, { width: size, height: size }]}>
      {isUploading ? (
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color="#0000ff" />
          <Text style={styles.loadingText}>Uploading...</Text>
        </View>
      ) : isLoading ? (
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="small" color="#0000ff" />
          <Text style={styles.loadingText}>Loading...</Text>
        </View>
      ) : displayUri ? (
        <View style={styles.imageContainer}>
          <Image
            source={{ uri: displayUri }}
            style={styles.image}
            resizeMode="cover"
            onError={(e) => {
              console.error("Image loading error:", e.nativeEvent.error);
              setError("Failed to load image");
            }}
          />
          {allowDelete && (
            <TouchableOpacity
              style={styles.deleteButton}
              onPress={handleDeleteImage}
            >
              <MaterialIcons name="delete" size={24} color="white" />
            </TouchableOpacity>
          )}
          {error && <Text style={styles.errorOverlay}>{error}</Text>}
        </View>
      ) : (
        <TouchableOpacity style={styles.placeholder} onPress={pickImage}>
          {placeholder || (
            <View style={styles.defaultPlaceholder}>
              <MaterialIcons
                name="add-photo-alternate"
                size={size / 3}
                color="#666"
              />
              <Text style={styles.placeholderText}>Add Image</Text>
            </View>
          )}
        </TouchableOpacity>
      )}

      {!isUploading && !isLoading && error && (
        <Text style={styles.errorText}>{error}</Text>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    borderRadius: 10,
    overflow: "hidden",
    backgroundColor: "#f5f5f5",
  },
  imageContainer: {
    width: "100%",
    height: "100%",
    position: "relative",
  },
  image: {
    width: "100%",
    height: "100%",
  },
  deleteButton: {
    position: "absolute",
    top: 5,
    right: 5,
    backgroundColor: "rgba(255, 0, 0, 0.7)",
    borderRadius: 15,
    width: 30,
    height: 30,
    alignItems: "center",
    justifyContent: "center",
    zIndex: 2,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
  },
  loadingText: {
    marginTop: 10,
    color: "#0000ff",
  },
  placeholder: {
    width: "100%",
    height: "100%",
    justifyContent: "center",
    alignItems: "center",
    backgroundColor: "#e0e0e0",
  },
  defaultPlaceholder: {
    alignItems: "center",
    justifyContent: "center",
  },
  placeholderText: {
    marginTop: 8,
    color: "#666",
    fontSize: 12,
  },
  errorText: {
    color: "red",
    textAlign: "center",
    marginTop: 5,
    fontSize: 12,
  },
  errorOverlay: {
    position: "absolute",
    bottom: 0,
    left: 0,
    right: 0,
    backgroundColor: "rgba(0, 0, 0, 0.7)",
    color: "white",
    textAlign: "center",
    padding: 5,
    fontSize: 10,
  },
});
