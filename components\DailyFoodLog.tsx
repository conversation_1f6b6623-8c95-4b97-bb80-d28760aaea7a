import React from "react";
import {
  View,
  Text,
  TouchableOpacity,
  Image,
  FlatList,
  Alert,
} from "react-native";
import { Ionicons } from "@expo/vector-icons";
import * as Haptics from "expo-haptics";
import { useRouter } from "expo-router";

import { useFoodLog, FoodItem } from "@/context/FoodLogContext";

interface DailyFoodLogProps {
  date: string;
}

export function DailyFoodLog({ date }: DailyFoodLogProps) {
  const router = useRouter();
  const { getLogsByDate, deleteFoodItem } = useFoodLog();

  const logs = getLogsByDate(date);

  // Group food items by meal type
  const groupedByMeal: Record<string, FoodItem[]> = logs.reduce(
    (groups, item) => {
      const group = groups[item.mealType] || [];
      group.push(item);
      groups[item.mealType] = group;
      return groups;
    },
    {} as Record<string, FoodItem[]>
  );

  // Sort meal types in logical order
  const mealOrder = ["breakfast", "lunch", "dinner", "snack"];
  const sortedMealTypes = Object.keys(groupedByMeal).sort(
    (a, b) => mealOrder.indexOf(a) - mealOrder.indexOf(b)
  );

  const handleAddFood = () => {
    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
    router.push("../add-food");
  };

  const handleDeleteFood = (id: string) => {
    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);

    Alert.alert(
      "Delete Food Item",
      "Are you sure you want to remove this item from your log?",
      [
        { text: "Cancel", style: "cancel" },
        {
          text: "Delete",
          style: "destructive",
          onPress: () => deleteFoodItem(id, date),
        },
      ]
    );
  };

  // If no logs, show empty state
  if (logs.length === 0) {
    return (
      <View className="bg-white/5 rounded-3xl p-6 mb-6">
        <Text className="text-xl font-bold text-white mb-4">
          Today's Food Log
        </Text>
        <View className="items-center justify-center py-8">
          <Ionicons name="restaurant-outline" size={64} color="#666" />
          <Text className="text-gray-400 text-center mt-4 mb-6">
            No food logged for today yet.
          </Text>
          <TouchableOpacity
            onPress={handleAddFood}
            className="bg-gradient-to-r from-blue-500 to-blue-600 px-6 py-3 rounded-full"
          >
            <Text className="text-white font-semibold">Add Food</Text>
          </TouchableOpacity>
        </View>
      </View>
    );
  }

  return (
    <View className="bg-white/5 rounded-3xl p-6 mb-6">
      <View className="flex-row justify-between items-center mb-4">
        <Text className="text-xl font-bold text-white">Today's Food Log</Text>
        <TouchableOpacity
          onPress={handleAddFood}
          className="bg-gradient-to-r from-blue-500 to-blue-600 px-4 py-2 rounded-full"
        >
          <Text className="text-white font-semibold">Add Food</Text>
        </TouchableOpacity>
      </View>

      {sortedMealTypes.map((mealType) => (
        <View key={mealType} className="mb-4">
          <Text className="text-gray-300 text-base font-semibold mb-2 capitalize">
            {mealType}
          </Text>

          {groupedByMeal[mealType].map((item) => (
            <View
              key={item.id}
              className="flex-row items-center bg-white/10 rounded-xl p-3 mb-2"
            >
              {item.image ? (
                <Image
                  source={{ uri: item.image }}
                  className="w-12 h-12 rounded-lg mr-3"
                />
              ) : (
                <View className="w-12 h-12 rounded-lg bg-gray-700 items-center justify-center mr-3">
                  <Ionicons name="restaurant" size={20} color="#888" />
                </View>
              )}

              <View className="flex-1">
                <Text className="text-white font-semibold">{item.name}</Text>
                <Text className="text-gray-400 text-xs">
                  {item.servingSize}
                </Text>
              </View>

              <View className="items-end">
                <Text className="text-white font-semibold">
                  {item.calories} kcal
                </Text>
                <Text className="text-gray-400 text-xs">
                  P: {item.protein}g | C: {item.carbs}g | F: {item.fat}g
                </Text>
              </View>

              <TouchableOpacity
                onPress={() => handleDeleteFood(item.id)}
                className="ml-2 p-2"
              >
                <Ionicons name="close-circle" size={22} color="#FF6B6B" />
              </TouchableOpacity>
            </View>
          ))}
        </View>
      ))}
    </View>
  );
}
