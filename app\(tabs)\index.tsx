import React, { useEffect, useState, useCallback } from "react";
import {
  TouchableOpacity,
  ActivityIndicator,
  Alert,
  ScrollView,
  View,
  Text,
  Modal,
  TouchableWithoutFeedback,
  Dimensions,
} from "react-native";
import Animated, {
  FadeIn,
  LinearTransition,
  ZoomIn,
  FadeInUp,
  FadeInDown,
  withTiming,
  withSpring,
} from "react-native-reanimated";
import * as ImagePicker from "expo-image-picker";
import { Camera } from "expo-camera";
import { Ionicons } from "@expo/vector-icons";
import { LinearGradient } from "expo-linear-gradient";
import { BlurView } from "expo-blur";
import { useRouter, type Route, useLocalSearchParams } from "expo-router";
import * as Haptics from "expo-haptics";
import Svg, { Circle } from "react-native-svg";
import { Image } from "expo-image";

import { useHistory } from "@/context/HistoryContext";
import { useUserProfile } from "@/context/UserProfileContext";
import { useFoodLog, FoodItem } from "@/context/FoodLogContext";
import { useExercise, Exercise, ExerciseType } from "@/context/ExerciseContext";
// DailyFoodLog and DailyExerciseLog components are not directly used for list rendering here, so imports can be removed if they were only for that.
// Keeping them for now in case they are used elsewhere or for type inspiration.
import { DailyFoodLog } from "@/components/DailyFoodLog";
import { DailyExerciseLog } from "@/components/DailyExerciseLog";
import { DailyProgressCard } from "@/components/DailyProgressCard";
import { WeekCalendar } from "@/components/WeekCalendar";
import { LogItemsList } from "@/components/LogItemsList";
import { AddItemModal } from "@/components/AddItemModal";
import { AnalyzingCard } from "@/components/AnalyzingCard";
import { getShadowStyle } from "@/utils/shadowStyles";
import {
  uploadImage,
  getImageUrl,
  downloadImage,
  prefetchImages,
} from "@/services/ImageService";
import { useAuth } from "@/context/AuthContext";
import { useTheme } from "@/context/ThemeContext"; // Added useTheme import

interface NutritionInfo {
  calories: string;
  protein?: string;
  carbs?: string;
  fat?: string;
  description?: string;
  healthScore?: number;
}

export default function HomeScreen() {
  const { resolvedTheme } = useTheme(); // Use resolvedTheme from context
  const router = useRouter();
  const params = useLocalSearchParams();
  const { profile, isProfileComplete } = useUserProfile();
  const { addToHistory } = useHistory();
  const {
    todayLogs, // Note: todayLogs itself might not be used directly if getLogsByDate is preferred
    getDailyNutrition,
    getLogsByDate,
    deleteFoodItem,
    addFoodItem,
  } = useFoodLog();
  const {
    calculateDailyCaloriesBurned,
    todayExercises, // Note: todayExercises itself might not be used directly if getExercisesByDate is preferred
    getExercisesByDate,
    deleteExercise, // Added deleteExercise
  } = useExercise();
  const { isAuthenticated } = useAuth();

  const EMPTY_ARRAY = React.useMemo(() => [], []); // Stable empty array reference

  const [selectedImage, setSelectedImage] = useState<string | null>(null);
  const [nutritionInfo, setNutritionInfo] = useState<NutritionInfo | null>(
    null
  );
  const [isLoading, setIsLoading] = useState(false);
  const [isModalVisible, setIsModalVisible] = useState(false);
  const [selectedDate, setSelectedDate] = useState(
    params.date?.toString() || new Date().toISOString().split("T")[0]
  );
  const [showMacros, setShowMacros] = useState(true);
  const [processedLogs, setProcessedLogs] = useState<FoodItem[]>([]);
  const isProcessingRef = React.useRef(false);
  const [forceUpdate, setForceUpdate] = useState(0);
  const [isAnalyzing, setIsAnalyzing] = useState(false);

  // New state for combined food and exercise logs
  type CombinedLogItem =
    | (FoodItem & { itemType: "food"; timestamp: Date })
    | (Exercise & { itemType: "exercise"; timestamp: Date });
  const [combinedLogItems, setCombinedLogItems] = useState<CombinedLogItem[]>(
    []
  );

  // windowWidth and calendarScrollRef moved to WeekCalendar component

  // Get selected date's logs and nutrition
  const dateNutrition = getDailyNutrition(selectedDate);
  const dateCaloriesBurned = calculateDailyCaloriesBurned(selectedDate);

  const dateLogs = React.useMemo(() => {
    const logs = getLogsByDate(selectedDate);
    return logs && logs.length > 0 ? logs : EMPTY_ARRAY;
  }, [getLogsByDate, selectedDate, EMPTY_ARRAY]);

  const dateExercises = React.useMemo(() => {
    const exercises = getExercisesByDate(selectedDate);
    return exercises && exercises.length > 0 ? exercises : EMPTY_ARRAY;
  }, [getExercisesByDate, selectedDate, EMPTY_ARRAY]);

  // Process image URLs for the logs
  const processFoodLogs = useCallback(async () => {
    // Skip if we're already in the middle of processing
    if (isProcessingRef.current) {
      console.log("Already processing logs, skipping");
      return;
    }

    // Set processing flag to prevent concurrent processing
    isProcessingRef.current = true;
    console.log("Processing food logs, total items:", dateLogs.length);

    try {
      // Create a Map to store already processed image URLs to avoid duplicate calls
      const processedImageUrls = new Map();

      // Start by prefetching all images to speed up future loading
      const imagePaths = dateLogs
        .filter((item) => item.imageUri && typeof item.imageUri === "string")
        .map((item) => item.imageUri as string);

      // Prefetch in the background while we process individual items
      if (imagePaths.length > 0) {
        prefetchImages(imagePaths).then((count) => {
          console.log(`Prefetched ${count} images in the background`);
        });
      }

      // Process each item to get its image URL
      const processedItems = await Promise.all(
        dateLogs.map(async (item) => {
          // If the item already has a valid image URL, return it as is
          if (
            item.image &&
            (item.image.startsWith("http") || item.image.startsWith("file:"))
          ) {
            return item;
          }

          // If the item has an imageUri field (path in Supabase storage), get the full URL
          if (item.imageUri) {
            try {
              // Check if we've already processed this image URI
              if (processedImageUrls.has(item.imageUri)) {
                console.log("Using cached URL for:", item.imageUri);
                return {
                  ...item,
                  image: processedImageUrls.get(item.imageUri),
                };
              }

              // Try to download the image to local cache first for faster future loading
              const localUri = await downloadImage(item.imageUri);
              if (localUri) {
                console.log(
                  "Got local cached image:",
                  localUri,
                  "for item:",
                  item.name
                );
                // Store in our temporary map to avoid duplicate calls
                processedImageUrls.set(item.imageUri, localUri);
                return { ...item, image: localUri };
              }

              // Fallback to signed URL if local caching failed
              const imageUrl = await getImageUrl(item.imageUri);
              if (imageUrl) {
                // Store in our temporary map to avoid duplicate calls
                processedImageUrls.set(item.imageUri, imageUrl);
                return { ...item, image: imageUrl };
              }
            } catch (error) {
              console.error("Error getting image URL:", error);
            }
          }

          // Return the original item if we couldn't get an image URL
          return item;
        })
      );

      // Create a Map using item.id as key to deduplicate
      const uniqueLogs = new Map();
      processedItems.forEach((item) => {
        uniqueLogs.set(item.id, item);
      });

      const uniqueLogsList = Array.from(uniqueLogs.values());

      console.log(
        "Processed logs (deduplicated):",
        uniqueLogsList.length,
        uniqueLogsList.map((log) => ({
          id: log.id,
          name: log.name,
          image: log.image,
          imageUri: log.imageUri,
        }))
      );

      setProcessedLogs(uniqueLogsList);
    } finally {
      // Always reset the processing flag when done
      isProcessingRef.current = false;
    }
  }, [dateLogs, prefetchImages, downloadImage, getImageUrl]); // Added dependencies for useCallback

  // Add this useEffect to process logs when the component mounts, selected date changes, or logs change
  useEffect(() => {
    if (dateLogs.length > 0) {
      processFoodLogs();
    } else {
      // Only update if processedLogs is not already empty
      setProcessedLogs((prev) => (prev.length === 0 ? prev : EMPTY_ARRAY));
    }
  }, [selectedDate, dateLogs, forceUpdate, processFoodLogs, EMPTY_ARRAY]);

  // useEffect to combine and sort food and exercise logs
  useEffect(() => {
    const foodItemsWithTimestamp: CombinedLogItem[] = processedLogs.map(
      (log) => ({
        ...log,
        itemType: "food",
        timestamp: new Date(log.createdAt),
      })
    );

    const exerciseItemsWithTimestamp: CombinedLogItem[] = dateExercises.map(
      (ex) => ({
        ...ex,
        itemType: "exercise",
        // Use createdAt if available, otherwise fall back to date
        timestamp: ex.createdAt
          ? new Date(ex.createdAt)
          : new Date(ex.date + "T00:00:00.000Z"),
      })
    );

    const combined = [...foodItemsWithTimestamp, ...exerciseItemsWithTimestamp];
    // Sort in descending order (newest first) by comparing timestamps
    combined.sort((a, b) => b.timestamp.getTime() - a.timestamp.getTime());
    setCombinedLogItems(combined);
  }, [processedLogs, dateExercises, selectedDate]);

  // Redirect to profile setup if not complete
  useEffect(() => {
    let isMounted = false; // Correctly initialize isMounted

    const redirectIfNeeded = () => {
      if (isMounted && !profile.initialized) {
        setTimeout(() => {
          if (isMounted) {
            try {
              router.push("/profile");
            } catch (error) {
              console.warn("Navigation error during profile redirect:", error); // Changed to warn
            }
          }
        }, 0);
      }
    };

    redirectIfNeeded();
    return () => {
      isMounted = false;
    };
  }, [profile.initialized, router]);

  // Calendar scroll functionality moved to WeekCalendar component

  const requestPermissions = async () => {
    const { status: cameraStatus } =
      await Camera.requestCameraPermissionsAsync();
    const { status: libraryStatus } =
      await ImagePicker.requestMediaLibraryPermissionsAsync();

    if (cameraStatus !== "granted" || libraryStatus !== "granted") {
      Alert.alert(
        "Permission required",
        "Please grant camera and photo library permissions to use this app."
      );
      return false;
    }
    return true;
  };

  const takePhoto = async () => {
    if (!(await requestPermissions())) return;

    try {
      setIsLoading(true);
      const result = await ImagePicker.launchCameraAsync({
        mediaTypes: ImagePicker.MediaTypeOptions.Images,
        allowsEditing: false,
        quality: 1,
      });

      if (!result.canceled && result.assets[0]) {
        const imageUri = result.assets[0].uri;
        setSelectedImage(imageUri);
        setNutritionInfo(null);
        // Start analyzing immediately
        analyzeImage(imageUri);
      } else {
        setIsLoading(false);
      }
    } catch (error) {
      console.error("Error taking photo:", error);
      Alert.alert("Error", "Failed to take photo. Please try again.");
      setIsLoading(false);
    }
  };

  const pickImage = async () => {
    if (!(await requestPermissions())) return;

    try {
      const result = await ImagePicker.launchImageLibraryAsync({
        mediaTypes: ImagePicker.MediaTypeOptions.Images,
        allowsEditing: false,
        quality: 1,
      });

      if (!result.canceled && result.assets[0]) {
        const imageUri = result.assets[0].uri;
        setSelectedImage(imageUri);
        setNutritionInfo(null);
        // Start analyzing immediately for gallery images too
        analyzeImage(imageUri);
      }
    } catch (error) {
      console.error("Error picking image:", error);
      Alert.alert("Error", "Failed to pick image. Please try again.");
    }
  };

  const analyzeImage = async (imageUri: string) => {
    if (!imageUri) return;

    // Don't start a new analysis if one is already in progress
    if (isAnalyzing) {
      console.log("Analysis already in progress, ignoring request");
      return;
    }

    try {
      setIsLoading(true);
      setIsAnalyzing(true); // Set analyzing flag
      setNutritionInfo(null);

      // Convert image to base64
      const response = await fetch(imageUri);
      const blob = await response.blob();
      const reader = new FileReader();
      reader.readAsDataURL(blob);
      reader.onloadend = async () => {
        const base64data = (reader.result as string)?.split(",")[1];
        if (!base64data) {
          Alert.alert(
            "Error",
            "Failed to process the image. Please try again."
          );
          setIsLoading(false);
          setIsAnalyzing(false); // Reset analyzing flag
          return;
        }

        try {
          const apiResponse = await fetch(
            "https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash-8b:generateContent?key=AIzaSyAxbxq1MwkGU9QPGcvODuNo906e9AQrSxo",
            {
              method: "POST",
              headers: {
                "Content-Type": "application/json",
              },
              body: JSON.stringify({
                contents: [
                  {
                    parts: [
                      {
                        text: "You are a food nutritionist.\nGiven the image, do the following:\nList all food items visible in the image (e.g. grilled chicken, white rice, broccoli).\nEstimate portion size for each item (e.g. 1 cup, 150g), Go with your lowest estimates.\nFor each item, return approximate calories, carbs (g), protein (g), and fat (g) using standard nutrition estimates.\nFinally, give a total breakdown of all items combined.",
                      },
                      {
                        inlineData: {
                          mimeType: "image/jpeg",
                          data: base64data,
                        },
                      },
                      {
                        text: 'Format your response as a valid JSON object ONLY, with no additional text. Include these keys with appropriate values: calories (number 0-2000), protein (number 0-100), carbs (number 0-200), fat (number 0-100), description (string), healthScore (number 1-10). Example: {"calories": 150, "protein": 5, "carbs": 30, "fat": 2, "description": "Fresh Blueberries", "healthScore": 9}',
                      },
                    ],
                  },
                ],
                generationConfig: {
                  temperature: 0,
                },
              }),
            }
          );

          if (!apiResponse.ok) {
            Alert.alert(
              "API Error",
              "Failed to analyze the image. Please try again later."
            );
            setIsLoading(false);
            setIsAnalyzing(false); // Reset analyzing flag
            return;
          }

          const data = await apiResponse.json();

          // Clean the response to ensure it's valid JSON
          const responseContent =
            data.candidates[0].content.parts[0].text.trim();

          // Extract JSON if it's wrapped in other text
          const jsonMatch = responseContent.match(/\{.*\}/s);
          const jsonString = jsonMatch ? jsonMatch[0] : responseContent;

          let parsedInfo = JSON.parse(jsonString);

          // Validate and sanitize the values
          parsedInfo = {
            calories: Math.min(
              2000,
              Math.max(0, parseInt(parsedInfo.calories?.toString() || "0"))
            ).toString(),
            protein: Math.min(
              100,
              Math.max(0, parseInt(parsedInfo.protein?.toString() || "0"))
            ).toString(),
            carbs: Math.min(
              200,
              Math.max(0, parseInt(parsedInfo.carbs?.toString() || "0"))
            ).toString(),
            fat: Math.min(
              100,
              Math.max(0, parseInt(parsedInfo.fat?.toString() || "0"))
            ).toString(),
            description: parsedInfo.description || "Food Analysis",
            healthScore: Math.min(
              10,
              Math.max(1, parseInt(parsedInfo.healthScore?.toString() || "5"))
            ),
          };

          // Validate that all required fields are present
          if (
            !parsedInfo.calories ||
            !parsedInfo.protein ||
            !parsedInfo.carbs ||
            !parsedInfo.fat
          ) {
            throw new Error("Missing required nutritional information");
          }

          setNutritionInfo(parsedInfo);

          // Upload image to Supabase and get persistent URL
          try {
            // Upload to private Supabase bucket and get the path
            const imagePath = await uploadImage(imageUri, "food");
            console.log("Uploaded image path:", imagePath);

            // Add to history with the path (not URL)
            addToHistory({
              image: imagePath || imageUri,
              info: parsedInfo,
              date: new Date(),
            });

            // Add directly to food log with the path in the image_url column
            await addFoodItem({
              name: parsedInfo.description || "Analyzed Food",
              calories: parseInt(parsedInfo.calories),
              protein: parseInt(parsedInfo.protein),
              carbs: parseInt(parsedInfo.carbs),
              fat: parseInt(parsedInfo.fat),
              servingSize: "1 serving",
              mealType: "lunch", // Default meal type
              date: selectedDate, // Use the selected date
              image: undefined, // Clear the old image field
              imageUri: imagePath || undefined, // Save the path, handle null case
            });

            // Immediately try to get the URL for this path to verify it works
            if (imagePath) {
              const testUrl = await getImageUrl(imagePath);
              console.log("Test URL for path:", testUrl);
            }
            // Trigger useEffect to run again by updating forceUpdate
            setForceUpdate((prev) => prev + 1);
          } catch (uploadError) {
            console.error("Failed to upload image to Supabase:", uploadError);

            // Continue with local image URI if upload fails
            // Still use the selected date instead of today's date
            // Add to history with the path (not URL)
            addToHistory({
              image: imageUri,
              info: parsedInfo,
              date: new Date(),
            });

            // Add directly to food log with local URI
            await addFoodItem({
              name: parsedInfo.description || "Analyzed Food",
              calories: parseInt(parsedInfo.calories),
              protein: parseInt(parsedInfo.protein),
              carbs: parseInt(parsedInfo.carbs),
              fat: parseInt(parsedInfo.fat),
              servingSize: "1 serving",
              mealType: "lunch", // Default meal type
              date: selectedDate, // Use the selected date
              image: undefined, // Clear the old image field
              imageUri: imageUri, // Local URI as fallback
            });

            setForceUpdate((prev) => prev + 1);
          }

          // Provide haptic feedback as confirmation
          Haptics.notificationAsync(Haptics.NotificationFeedbackType.Success);

          // Only set loading to false, but keep the image and nutrition info to display in the card
          setIsLoading(false);
          setIsAnalyzing(false); // Reset analyzing flag
          // Keep selectedImage and nutritionInfo states
        } catch (e) {
          Alert.alert(
            "Analysis Error",
            "Could not analyze the food properly. Please try again."
          );
          setIsLoading(false);
          setIsAnalyzing(false); // Reset analyzing flag
        }
      };
    } catch (error) {
      Alert.alert("Error", "Failed to analyze the image. Please try again.");
      setIsLoading(false);
      setIsAnalyzing(false); // Reset analyzing flag
    }
  };

  const resetImage = () => {
    setSelectedImage(null);
    setNutritionInfo(null);
  };

  const addAnalyzedFoodToLog = async () => {
    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);

    if (!nutritionInfo) return;

    try {
      const params = new URLSearchParams({
        name: nutritionInfo.description || "Analyzed Food",
        calories: nutritionInfo.calories,
        protein: nutritionInfo.protein || "",
        carbs: nutritionInfo.carbs || "",
        fat: nutritionInfo.fat || "",
        image: selectedImage || "",
        date: selectedDate,
      }).toString();

      router.push(`../add-food?${params}`);
    } catch (error) {
      Alert.alert("Error", "Failed to add food to log. Please try again.");
    }
  };

  // Navigation helpers
  const navigateTo = (path: Route) => {
    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
    router.push(path);
  };

  // Modal functions
  const toggleModal = () => {
    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
    setIsModalVisible(!isModalVisible);
  };

  const handleSelectTakePhoto = () => {
    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);
    takePhoto();
    setIsModalVisible(false);
  };

  const handleSelectUploadPhoto = () => {
    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);
    pickImage();
    setIsModalVisible(false);
  };

  const handleSelectFood = () => {
    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);
    router.push(`/add-food?date=${selectedDate}`);
    setIsModalVisible(false);
  };

  const handleSelectWorkout = () => {
    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);
    router.push(`/add-exercise?date=${selectedDate}`);
    setIsModalVisible(false);
  };

  // Helper function to select a date
  const selectDate = (dateString: string) => {
    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
    setSelectedDate(dateString);
  };

  // renderDailyProgress function moved to DailyProgressCard component

  // const renderExerciseSummary = () => { ... }; // This function will be removed

  const renderAnalyzingCard = () => {
    return (
      <AnalyzingCard
        selectedImage={selectedImage || ""}
        isLoading={isLoading}
      />
    );
  };

  return (
    <View
      className={`flex-1 ${
        resolvedTheme === "light" ? "bg-gray-50" : "bg-gray-950"
      }`}
    >
      <LinearGradient
        colors={
          resolvedTheme === "light"
            ? ["#f8fafc", "#e2e8f0", "#f1f5f9"]
            : ["#111827", "#1F2937"]
        }
        className="flex-1"
      >
        <ScrollView
          className="flex-1 px-4 pt-6"
          showsVerticalScrollIndicator={false}
        >
          <Animated.View
            entering={FadeInDown.duration(500)}
            className="flex-row justify-between items-center mb-6"
          >
            <View>
              <Text
                className={`text-3xl font-bold ${
                  resolvedTheme === "light" ? "text-gray-800" : "text-white"
                }`}
              >
                Calorie AI
              </Text>
            </View>
            <TouchableOpacity
              onPress={() => navigateTo("/profile")}
              className={`p-3 rounded-full border ${
                resolvedTheme === "light"
                  ? "bg-white border-gray-200/60"
                  : "bg-gray-800 border-gray-700"
              }`}
              style={getShadowStyle(resolvedTheme, "button")}
            >
              <Ionicons
                name="person"
                size={24}
                color={resolvedTheme === "light" ? "#4b5563" : "white"}
              />
            </TouchableOpacity>
          </Animated.View>

          {/* Day of week selector */}
          <WeekCalendar
            selectedDate={selectedDate}
            onDateSelect={selectDate}
            getDailyNutrition={getDailyNutrition}
            profile={profile}
          />

          {/* Daily calorie progress */}
          <DailyProgressCard
            dateNutrition={dateNutrition}
            dateCaloriesBurned={dateCaloriesBurned}
            profile={profile}
          />

          {/* Combined log */}
          <LogItemsList
            combinedLogItems={combinedLogItems}
            isLoading={isLoading}
            selectedImage={selectedImage}
            selectedDate={selectedDate}
            deleteFoodItem={deleteFoodItem}
            deleteExercise={deleteExercise}
            setForceUpdate={setForceUpdate}
            renderAnalyzingCard={renderAnalyzingCard}
          />

          {/* Exercise summary is now part of renderDailyProgress */}
          {/* {renderExerciseSummary()} */}
        </ScrollView>
      </LinearGradient>

      {/* Floating Action Button */}
      <TouchableOpacity
        activeOpacity={0.85}
        onPress={toggleModal}
        className="absolute bottom-24 right-6 w-16 h-16 rounded-full items-center justify-center z-10"
      >
        <LinearGradient
          colors={
            resolvedTheme === "light"
              ? ["#374151", "#1F2937"]
              : ["#f3f4f6", "#e5e5e5"]
          }
          start={{ x: 0, y: 0 }}
          end={{ x: 1, y: 1 }}
          style={{
            position: "absolute",
            left: 0,
            top: 0,
            right: 0,
            bottom: 0,
            borderRadius: 9999,
          }}
        />
        <Ionicons
          name="add"
          size={36}
          color={resolvedTheme === "light" ? "#fff" : "#222"}
          style={{ zIndex: 1 }}
        />
      </TouchableOpacity>

      {/* Modal Popup */}
      <AddItemModal
        isVisible={isModalVisible}
        onClose={() => setIsModalVisible(false)}
        onSelectTakePhoto={handleSelectTakePhoto}
        onSelectUploadPhoto={handleSelectUploadPhoto}
        onSelectFood={handleSelectFood}
        onSelectWorkout={handleSelectWorkout}
      />
    </View>
  );
}
