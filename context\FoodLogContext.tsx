import React, {
  createContext,
  useState,
  useContext,
  ReactNode,
  useEffect,
} from "react";
import AsyncStorage from "@react-native-async-storage/async-storage";
import {
  getAllFoodLogs,
  addFoodItem as addFoodItemToSupabase,
  deleteFoodItem as deleteFoodItemFromSupabase,
  clearFoodLogsForDay as clearFoodLogsForDayFromSupabase,
} from "../services/FoodLogService";

export interface FoodItem {
  id: string;
  name: string;
  calories: number;
  protein: number;
  carbs: number;
  fat: number;
  servingSize: string;
  imageUri?: string;
  image?: string;
  date: string; // ISO date string YYYY-MM-DD
  mealType: "breakfast" | "lunch" | "dinner" | "snack";
  createdAt: number; // timestamp for sorting
}

interface DailyLog {
  [date: string]: FoodItem[];
}

interface FoodLogContextType {
  logs: DailyLog;
  todayLogs: FoodItem[];
  addFoodItem: (item: Omit<FoodItem, "id" | "createdAt">) => Promise<void>;
  deleteFoodItem: (id: string, date: string) => Promise<void>;
  clearLogsForDay: (date: string) => Promise<void>;
  getDailyNutrition: (date: string) => {
    calories: number;
    protein: number;
    carbs: number;
    fat: number;
  };
  getLogsByDate: (date: string) => FoodItem[];
  getLogsByMealType: (
    date: string,
    mealType: FoodItem["mealType"]
  ) => FoodItem[];
  isLoading: boolean;
}

export const FoodLogContext = createContext<FoodLogContextType>({
  logs: {},
  todayLogs: [],
  addFoodItem: async () => {},
  deleteFoodItem: async () => {},
  clearLogsForDay: async () => {},
  getDailyNutrition: () => ({ calories: 0, protein: 0, carbs: 0, fat: 0 }),
  getLogsByDate: () => [],
  getLogsByMealType: () => [],
  isLoading: true,
});

interface FoodLogProviderProps {
  children: ReactNode;
}

export function FoodLogProvider({ children }: FoodLogProviderProps) {
  const [logs, setLogs] = useState<DailyLog>({});
  const [isLoading, setIsLoading] = useState(true);

  // Get today's date in YYYY-MM-DD format
  const getTodayDateString = () => {
    const today = new Date();
    return today.toISOString().split("T")[0];
  };

  // Get today's logs
  const todayLogs = logs[getTodayDateString()] || [];

  // Load logs from Supabase on component mount
  useEffect(() => {
    const loadLogs = async () => {
      try {
        setIsLoading(true);

        // Try to load logs from Supabase
        const supabaseLogs = await getAllFoodLogs();

        if (Object.keys(supabaseLogs).length > 0) {
          setLogs(supabaseLogs);
        } else {
          // Fallback to local storage if no logs in Supabase
          const storedLogs = await AsyncStorage.getItem("foodLogs");
          if (storedLogs) {
            const parsedLogs = JSON.parse(storedLogs);
            setLogs(parsedLogs);

            // Migrate each food item to Supabase
            for (const date in parsedLogs) {
              for (const item of parsedLogs[date]) {
                const { id, createdAt, ...rest } = item;
                await addFoodItemToSupabase(rest);
              }
            }

            // Clear local storage after migration
            await AsyncStorage.removeItem("foodLogs");
          }
        }
      } catch (error) {
        console.error("Failed to load food logs:", error);
      } finally {
        setIsLoading(false);
      }
    };

    loadLogs();
  }, []);

  // Add a new food item to the log
  const addFoodItem = async (item: Omit<FoodItem, "id" | "createdAt">) => {
    try {
      // Add to Supabase
      const newItem = await addFoodItemToSupabase(item);

      if (newItem) {
        // Update local state
        setLogs((prevLogs) => {
          const dateItems = prevLogs[item.date] || [];
          return {
            ...prevLogs,
            [item.date]: [...dateItems, newItem],
          };
        });
      } else {
        // Fallback to local storage if Supabase fails
        const id = Math.random().toString(36).substring(2, 15);
        const localItem: FoodItem = {
          ...item,
          id,
          createdAt: Date.now(),
        };

        setLogs((prevLogs) => {
          const dateItems = prevLogs[item.date] || [];
          const updatedLogs = {
            ...prevLogs,
            [item.date]: [...dateItems, localItem],
          };

          // Save to local storage as fallback
          AsyncStorage.setItem("foodLogs", JSON.stringify(updatedLogs));

          return updatedLogs;
        });
      }
    } catch (error) {
      console.error("Failed to add food item:", error);
    }
  };

  // Delete a food item from the log
  const deleteFoodItem = async (id: string, date: string) => {
    try {
      // Delete from Supabase
      const success = await deleteFoodItemFromSupabase(id);

      if (success || !success) {
        // Continue removing from UI even if DB operation failed
        // Update local state
        setLogs((prevLogs) => {
          if (!prevLogs[date]) return prevLogs;

          const updatedDateLogs = prevLogs[date].filter(
            (item) => item.id !== id
          );
          return {
            ...prevLogs,
            [date]: updatedDateLogs,
          };
        });
      }
    } catch (error) {
      console.error("Failed to delete food item:", error);
    }
  };

  // Clear all logs for a specific day
  const clearLogsForDay = async (date: string) => {
    try {
      // Clear from Supabase
      const success = await clearFoodLogsForDayFromSupabase(date);

      // Update local state regardless of Supabase success
      setLogs((prevLogs) => {
        const { [date]: _, ...rest } = prevLogs;
        return rest;
      });
    } catch (error) {
      console.error("Failed to clear logs for day:", error);
    }
  };

  // Calculate total nutrition for a specific day
  const getDailyNutrition = (date: string) => {
    const dayLogs = logs[date] || [];

    return dayLogs.reduce(
      (totals, item) => {
        return {
          calories: totals.calories + item.calories,
          protein: totals.protein + item.protein,
          carbs: totals.carbs + item.carbs,
          fat: totals.fat + item.fat,
        };
      },
      { calories: 0, protein: 0, carbs: 0, fat: 0 }
    );
  };

  // Get logs for a specific date
  const getLogsByDate = (date: string) => {
    return logs[date] || [];
  };

  // Get logs for a specific date and meal type
  const getLogsByMealType = (date: string, mealType: FoodItem["mealType"]) => {
    const dayLogs = logs[date] || [];
    return dayLogs.filter((item) => item.mealType === mealType);
  };

  return (
    <FoodLogContext.Provider
      value={{
        logs,
        todayLogs,
        addFoodItem,
        deleteFoodItem,
        clearLogsForDay,
        getDailyNutrition,
        getLogsByDate,
        getLogsByMealType,
        isLoading,
      }}
    >
      {children}
    </FoodLogContext.Provider>
  );
}

export const useFoodLog = () => useContext(FoodLogContext);
