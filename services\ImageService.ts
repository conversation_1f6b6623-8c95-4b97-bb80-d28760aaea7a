import { supabase, getCurrentUserId } from "../config/supabase";
import * as FileSystem from "expo-file-system";
import { Platform } from "react-native";
import { Image } from "expo-image"; // Import expo-image for prefetching

// Supabase configuration constants
const SUPABASE_URL = "https://awwwqyzndgsxkcplkwki.supabase.co";
const SUPABASE_KEY =
  "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImF3d3dxeXpuZGdzeGtjcGxrd2tpIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDI2NjIzOTIsImV4cCI6MjA1ODIzODM5Mn0.uOA5irjBkwueqzml0W2GtTApseRZHzlOo4fP2Zb8EtU";

// Cache for storing signed URLs to avoid repeated requests
const urlCache = new Map<string, { url: string; expiry: number }>();
const CACHE_EXPIRY = 23 * 60 * 60 * 1000; // 23 hours (slightly less than the 24h signed URL)

/**
 * Upload an image to Supabase Storage using device ID for folder structure
 * @param uri Local URI of the image to upload
 * @param filePrefix Optional prefix to add to the filename (default: "image")
 * @returns Promise with the path of the uploaded image
 */
export const uploadImage = async (
  uri: string,
  filePrefix = "image"
): Promise<string | null> => {
  try {
    // Use user ID for folder structure
    const userId = await getCurrentUserId();
    if (!userId) {
      console.error(
        "User ID not available. Please ensure you are authenticated."
      );
      return null;
    }
    const folderPath = `${userId}`;

    // Generate a unique filename
    const timestamp = new Date().getTime();
    const randomString = Math.random().toString(36).substring(2, 8);
    const fileName = `${filePrefix}-${timestamp}-${randomString}.jpg`;
    const filePath = `${folderPath}/${fileName}`;

    // For file:// URIs, first try using direct fetch API which works well on most platforms
    if (uri.startsWith("file:")) {
      try {
        // Alternative approach using direct fetch to Supabase REST API
        // This works more consistently across platforms
        const formData = new FormData();
        formData.append("file", {
          uri: uri,
          name: fileName,
          type: "image/jpeg",
        } as any);

        // Get auth token to use in headers
        const {
          data: { session },
        } = await supabase.auth.getSession();
        const token = session?.access_token;

        if (!token) {
          console.error("No authentication token available");
          return null;
        }

        // Use fetch API directly to upload
        const response = await fetch(
          `${SUPABASE_URL}/storage/v1/object/images/${filePath}`,
          {
            method: "POST",
            headers: {
              Authorization: `Bearer ${token}`,
              "x-upsert": "true",
            },
            body: formData,
          }
        );

        const jsonResponse = await response.json();

        if (!response.ok) {
          console.error("Error uploading via fetch:", jsonResponse);
          throw new Error("Upload failed with status: " + response.status);
        }

        console.log("Image uploaded successfully to path:", filePath);
        return filePath;
      } catch (fetchError) {
        console.error(
          "Error using fetch upload, trying fallback method:",
          fetchError
        );

        // Fallback to blob method if fetch fails
        try {
          // Read the file as base64
          const base64Data = await FileSystem.readAsStringAsync(uri, {
            encoding: FileSystem.EncodingType.Base64,
          });

          // Convert base64 to Blob for Supabase
          const blob = await fetch(`data:image/jpeg;base64,${base64Data}`).then(
            (res) => res.blob()
          );

          // Upload blob to Supabase
          const { data, error } = await supabase.storage
            .from("images")
            .upload(filePath, blob, {
              contentType: "image/jpeg",
              upsert: true,
            });

          if (error) {
            console.error("Error uploading image:", error);
            return null;
          }

          console.log(
            "Image uploaded successfully to path (fallback method):",
            filePath
          );
          return filePath;
        } catch (blobError) {
          console.error("Error in blob upload fallback:", blobError);
          return null;
        }
      }
    } else {
      console.error("Unsupported image URI format:", uri);
      return null;
    }
  } catch (error) {
    console.error("Error in uploadImage:", error);
    return null;
  }
};

/**
 * Get a signed URL for a private image in Supabase Storage
 * @param path Full path of the image in the bucket (e.g., "deviceId/image-123.jpg")
 * @returns Promise with the signed URL for accessing the image
 */
export const getImageUrl = async (path: string): Promise<string | null> => {
  try {
    // If it's already a full URL, return it as is
    if (path.startsWith("http")) {
      return path;
    }

    // If it's a file:// URI, return as is (local file)
    if (path.startsWith("file:")) {
      return path;
    }

    // Check if path is valid
    if (!path || path.trim() === "") {
      console.error("Invalid path provided to getImageUrl:", path);
      return null;
    }

    // Check cache first
    const now = Date.now();
    const cachedItem = urlCache.get(path);
    if (cachedItem && cachedItem.expiry > now) {
      // Don't log every cache hit to reduce console noise
      return cachedItem.url;
    }

    console.log("Getting signed URL for path:", path);

    // Get a signed URL for the private image with longer expiry (24 hours)
    const { data, error } = await supabase.storage
      .from("images")
      .createSignedUrl(path, 60 * 60 * 24); // 24 hour expiry

    if (error) {
      console.error("Error creating signed URL:", error);

      // Fallback: Try to access via authenticated endpoint if signed URL fails
      try {
        // Get session token
        const {
          data: { session },
        } = await supabase.auth.getSession();
        if (session?.access_token) {
          // Format authenticated URL
          const authUrl = `${SUPABASE_URL}/storage/v1/object/authenticated/images/${path}`;
          console.log("Fallback to authenticated URL:", authUrl);

          // Cache the URL
          urlCache.set(path, {
            url: authUrl,
            expiry: now + CACHE_EXPIRY,
          });

          return authUrl;
        }
      } catch (authError) {
        console.error("Error creating authenticated URL:", authError);
      }

      return null;
    }

    // Cache the URL
    urlCache.set(path, {
      url: data.signedUrl,
      expiry: now + CACHE_EXPIRY,
    });

    return data.signedUrl;
  } catch (error) {
    console.error("Error in getImageUrl:", error);
    return null;
  }
};

/**
 * Download an image from Supabase Storage to local filesystem
 * @param path Path of the image in the storage bucket
 * @returns Promise with the local URI of the downloaded image
 */
export const downloadImage = async (path: string): Promise<string | null> => {
  try {
    // First get a signed URL
    const signedUrl = await getImageUrl(path);
    if (!signedUrl) return null;

    // Make sure cacheDirectory exists before using it
    const cacheDir = FileSystem.cacheDirectory || FileSystem.documentDirectory;
    if (!cacheDir) {
      console.error("No valid cache directory available");
      return null;
    }

    // Get a safe filename from the path
    const fileName = path.split("/").pop();
    if (!fileName) {
      console.error("Could not extract filename from path");
      return null;
    }

    // Create a unique cache path that's easy to look up again
    const localFilePath = cacheDir + "cached_images/" + fileName;
    const cacheDirectory = cacheDir + "cached_images/";

    // Ensure the directory exists before attempting download
    try {
      const dirInfo = await FileSystem.getInfoAsync(cacheDirectory);
      if (!dirInfo.exists) {
        await FileSystem.makeDirectoryAsync(cacheDirectory, {
          intermediates: true,
        });
        console.log("Created cache directory:", cacheDirectory);
      }
    } catch (error) {
      console.error("Failed to create cache directory:", error);
      return null;
    }

    // Check if the file already exists in cache
    try {
      const fileInfo = await FileSystem.getInfoAsync(localFilePath);
      if (fileInfo.exists) {
        console.log("Using cached local file:", localFilePath);
        return localFilePath;
      }
    } catch (error) {
      console.log("Error checking file existence:", error);
    }

    // Download the image to local filesystem
    const downloadResumable = FileSystem.createDownloadResumable(
      signedUrl,
      localFilePath,
      {},
      (downloadProgress) => {
        const progress =
          downloadProgress.totalBytesWritten /
          downloadProgress.totalBytesExpectedToWrite;
        // You can use this progress value to show a progress indicator
      }
    );

    const downloadResult = await downloadResumable.resumeAsync();
    if (!downloadResult || !downloadResult.uri) {
      console.error("Download failed or URI is missing");
      return null;
    }

    return downloadResult.uri;
  } catch (error) {
    console.error("Error downloading image:", error);
    return null;
  }
};

/**
 * Prefetch a list of images by their paths
 * @param paths Array of image paths to prefetch
 * @returns Promise with number of successfully prefetched images
 */
export const prefetchImages = async (paths: string[]): Promise<number> => {
  if (!paths || !paths.length) return 0;

  let successCount = 0;

  try {
    // Get signed URLs for all valid paths
    const urlPromises = paths
      .filter(
        (path) =>
          path &&
          typeof path === "string" &&
          !path.startsWith("file:") &&
          !path.startsWith("http")
      )
      .map((path) => getImageUrl(path));

    const urls = await Promise.all(urlPromises);
    const validUrls = urls.filter((url) => url !== null) as string[];

    if (validUrls.length > 0) {
      // Use expo-image prefetch which offers better caching
      const prefetchResult = await Image.prefetch(validUrls);
      if (prefetchResult) {
        console.log(`Successfully prefetched ${validUrls.length} images`);
        successCount = validUrls.length;
      }
    }

    return successCount;
  } catch (error) {
    console.error("Error prefetching images:", error);
    return successCount;
  }
};

/**
 * Clear the in-memory URL cache
 */
export const clearUrlCache = () => {
  urlCache.clear();
  console.log("URL cache cleared");
};

/**
 * Delete an image from Supabase Storage
 * @param path Path of the image in the storage bucket
 * @returns Promise indicating success or failure
 */
export const deleteImage = async (path: string): Promise<boolean> => {
  try {
    if (!path) return false;

    // Only delete if it's a storage path, not a file:// or http:// URL
    if (path.startsWith("file:") || path.startsWith("http")) {
      return true;
    }

    const { error } = await supabase.storage.from("images").remove([path]);

    if (error) {
      console.error("Error deleting image:", error);
      return false;
    }

    // Remove from cache if exists
    if (urlCache.has(path)) {
      urlCache.delete(path);
    }

    return true;
  } catch (error) {
    console.error("Error in deleteImage:", error);
    return false;
  }
};

/**
 * Extract the image path from a Supabase Storage URL
 * @param url Full Supabase Storage URL
 * @returns The path component that can be used with other functions
 */
export const getPathFromUrl = (url: string | null): string | null => {
  if (!url) return null;

  try {
    // Extract the path from the URL by removing the base storage URL
    const pathMatch = url.match(/\/storage\/v1\/object\/public\/images\/(.*)/);
    if (pathMatch && pathMatch[1]) {
      return decodeURIComponent(pathMatch[1]);
    }

    // If it's a signed URL, extract differently
    const signedMatch = url.match(
      /\/storage\/v1\/object\/sign\/images\/(.*?)\?/
    );
    if (signedMatch && signedMatch[1]) {
      return decodeURIComponent(signedMatch[1]);
    }

    return null;
  } catch (error) {
    console.error("Error parsing image URL:", error);
    return null;
  }
};
