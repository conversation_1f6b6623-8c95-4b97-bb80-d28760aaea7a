import React, {
  createContext,
  useState,
  useContext,
  useEffect,
  ReactNode,
} from "react";
import { ensureAuthenticated, getCurrentUserId } from "@/config/supabase";

interface AuthContextType {
  isAuthenticated: boolean;
  userId: string | null;
  isLoading: boolean;
  initialize: () => Promise<boolean>;
}

// Default context values
const defaultContext: AuthContextType = {
  isAuthenticated: false,
  userId: null,
  isLoading: true,
  initialize: async () => false,
};

// Create the context
export const AuthContext = createContext<AuthContextType>(defaultContext);

// Provider component
interface AuthProviderProps {
  children: ReactNode;
}

export function AuthProvider({ children }: AuthProviderProps) {
  const [isAuthenticated, setIsAuthenticated] = useState(false);
  const [userId, setUserId] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  // Initialize auth state once on app start
  useEffect(() => {
    initialize();
  }, []);

  // Function to initialize or reinitialize auth state
  const initialize = async (): Promise<boolean> => {
    try {
      setIsLoading(true);

      // Call the authentication method
      const authStatus = await ensureAuthenticated();
      setIsAuthenticated(authStatus);

      if (authStatus) {
        // Get the user ID if authenticated
        const id = await getCurrentUserId();
        setUserId(id);
      } else {
        setUserId(null);
      }

      setIsLoading(false);
      return authStatus;
    } catch (error) {
      console.error("Error initializing authentication:", error);
      setIsAuthenticated(false);
      setUserId(null);
      setIsLoading(false);
      return false;
    }
  };

  return (
    <AuthContext.Provider
      value={{
        isAuthenticated,
        userId,
        isLoading,
        initialize,
      }}
    >
      {children}
    </AuthContext.Provider>
  );
}

// Hook for easy context consumption
export const useAuth = () => useContext(AuthContext);
