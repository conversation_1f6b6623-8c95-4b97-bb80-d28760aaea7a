import {
  DarkTheme,
  De<PERSON>ultTheme,
  ThemeProvider as NavigationThemeProvider,
} from "@react-navigation/native";
import { useFonts } from "expo-font";
import { Stack } from "expo-router";
import * as SplashScreen from "expo-splash-screen";
import { StatusBar } from "expo-status-bar";
import { useEffect } from "react";
import "react-native-reanimated";
import { useColorScheme } from "nativewind";
import { ThemeProvider, useTheme } from "@/context/ThemeContext";
import { UserProfileProvider } from "@/context/UserProfileContext";
import { FoodLogProvider } from "@/context/FoodLogContext";
import { WeightProvider } from "@/context/WeightContext";
import { ExerciseProvider } from "@/context/ExerciseContext";
import { NotificationsProvider } from "@/context/NotificationsContext";
import { AuthProvider } from "@/context/AuthContext";
import "../global.css";
import { setBackgroundColorAsync } from "expo-system-ui";
import { SafeAreaProvider } from "react-native-safe-area-context";

// Prevent the splash screen from auto-hiding before asset loading is complete.
SplashScreen.preventAutoHideAsync();

// Main app without ThemeProvider so we can use hooks
function RootLayoutNav() {
  const { resolvedTheme, isLoading: themeLoading } = useTheme();
  const { colorScheme } = useColorScheme();
  const [loaded] = useFonts({
    SpaceMono: require("../assets/fonts/SpaceMono-Regular.ttf"),
  });

  useEffect(() => {
    // Set the system UI background color to match our theme
    setBackgroundColorAsync(resolvedTheme === "dark" ? "#111827" : "#ffffff");
  }, [resolvedTheme]);

  useEffect(() => {
    // Only hide splash screen when both fonts and theme are loaded
    if (loaded && !themeLoading) {
      SplashScreen.hideAsync();
    }
  }, [loaded, themeLoading]);

  // Don't render until both fonts and theme are loaded
  if (!loaded || themeLoading) {
    return null;
  }

  return (
    <SafeAreaProvider>
      <StatusBar style={resolvedTheme === "dark" ? "light" : "dark"} />
      <AuthProvider>
        <UserProfileProvider>
          <FoodLogProvider>
            <WeightProvider>
              <ExerciseProvider>
                <NotificationsProvider>
                  <NavigationThemeProvider
                    value={resolvedTheme === "dark" ? DarkTheme : DefaultTheme}
                  >
                    <Stack
                      screenOptions={{
                        headerShown: false,
                        animation: "slide_from_right",
                        contentStyle: {
                          backgroundColor:
                            resolvedTheme === "dark" ? "#111827" : "#ffffff",
                        },
                      }}
                    >
                      <Stack.Screen
                        name="(tabs)"
                        options={{ headerShown: false }}
                      />
                      <Stack.Screen name="profile" />
                      <Stack.Screen name="add-food" />
                      <Stack.Screen name="add-exercise" />
                      <Stack.Screen name="add-weight" />
                      <Stack.Screen name="notifications" />
                      <Stack.Screen
                        name="scanner"
                        options={{ presentation: "fullScreenModal" }}
                      />
                      <Stack.Screen name="social" />
                      <Stack.Screen
                        name="(auth)"
                        options={{ headerShown: false }}
                      />
                      <Stack.Screen
                        name="not-found"
                        options={{ title: "Oops!" }}
                      />
                    </Stack>
                  </NavigationThemeProvider>
                </NotificationsProvider>
              </ExerciseProvider>
            </WeightProvider>
          </FoodLogProvider>
        </UserProfileProvider>
      </AuthProvider>
    </SafeAreaProvider>
  );
}

// Root layout with ThemeProvider
export default function RootLayout() {
  return (
    <ThemeProvider>
      <RootLayoutNav />
    </ThemeProvider>
  );
}
