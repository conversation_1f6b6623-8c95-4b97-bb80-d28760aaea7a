import React, { useState, useEffect } from "react";
import {
  View,
  Text,
  TouchableOpacity,
  TextInput,
  Alert,
  StyleSheet,
  Animated,
  ScrollView,
} from "react-native";
import { useRouter } from "expo-router";
import { Ionicons } from "@expo/vector-icons";
import { LinearGradient } from "expo-linear-gradient";
import * as Haptics from "expo-haptics";
import { useWeight } from "../context/WeightContext";
import { SafeAreaView } from "react-native-safe-area-context";

export default function AddWeightScreen() {
  const router = useRouter();
  const { addWeightEntry } = useWeight();
  const [weight, setWeight] = useState("");
  const [isMetric, setIsMetric] = useState(true);
  const [isLoading, setIsLoading] = useState(false);

  // Animation values
  const translateY = new Animated.Value(50);
  const opacity = new Animated.Value(0);

  useEffect(() => {
    // Animate the component in
    Animated.parallel([
      Animated.timing(translateY, {
        toValue: 0,
        duration: 500,
        useNativeDriver: true,
      }),
      Animated.timing(opacity, {
        toValue: 1,
        duration: 400,
        useNativeDriver: true,
      }),
    ]).start();
  }, []);

  const handleAddWeight = async () => {
    if (!weight || isNaN(Number(weight)) || Number(weight) <= 0) {
      Alert.alert("Invalid Entry", "Please enter a valid weight");
      return;
    }

    try {
      setIsLoading(true);

      // Provide haptic feedback
      Haptics.notificationAsync(Haptics.NotificationFeedbackType.Success);

      // Convert to kg if in imperial
      const weightValue = isMetric
        ? parseFloat(weight)
        : parseFloat((parseFloat(weight) * 0.453592).toFixed(1));

      // Call addWeightEntry with the correct parameters
      await addWeightEntry(weightValue, isMetric);

      Alert.alert(
        "Weight Added",
        "Your weight has been recorded successfully",
        [{ text: "OK", onPress: () => router.back() }]
      );
    } catch (error) {
      console.error("Error adding weight:", error);
      Alert.alert("Error", "Failed to add weight entry");
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <SafeAreaView style={styles.container}>
      <LinearGradient
        colors={["#4c669f", "#3b5998", "#192f6a"]}
        style={styles.gradient}
      >
        <View style={styles.header}>
          <TouchableOpacity
            style={styles.backButton}
            onPress={() => {
              setTimeout(() => {
                router.back();
              }, 0);
            }}
          >
            <Text style={styles.backButtonText}>Cancel</Text>
          </TouchableOpacity>
          <Text style={styles.title}>Add Weight</Text>
          <View style={{ width: 60 }} />
        </View>

        <View style={styles.form}>
          <View style={styles.inputContainer}>
            <TextInput
              style={styles.input}
              keyboardType="numeric"
              value={weight}
              onChangeText={setWeight}
              placeholder="Enter weight"
              placeholderTextColor="#aaa"
            />
            <TouchableOpacity
              style={styles.unitToggle}
              onPress={() => setIsMetric(!isMetric)}
            >
              <Text style={styles.unitText}>{isMetric ? "kg" : "lb"}</Text>
            </TouchableOpacity>
          </View>

          <TouchableOpacity
            style={styles.addButton}
            onPress={handleAddWeight}
            disabled={isLoading}
          >
            <Text style={styles.addButtonText}>
              {isLoading ? "Adding..." : "Add Weight"}
            </Text>
          </TouchableOpacity>
        </View>
      </LinearGradient>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  gradient: {
    flex: 1,
    paddingHorizontal: 20,
  },
  header: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    marginVertical: 20,
  },
  backButton: {
    width: 60,
  },
  backButtonText: {
    color: "white",
    fontSize: 16,
  },
  title: {
    color: "white",
    fontSize: 20,
    fontWeight: "bold",
  },
  form: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
    marginBottom: 100,
  },
  inputContainer: {
    flexDirection: "row",
    alignItems: "center",
    marginBottom: 30,
    width: "100%",
  },
  input: {
    flex: 1,
    backgroundColor: "rgba(255, 255, 255, 0.2)",
    color: "white",
    fontSize: 24,
    padding: 15,
    borderRadius: 10,
    marginRight: 10,
  },
  unitToggle: {
    backgroundColor: "rgba(255, 255, 255, 0.3)",
    padding: 15,
    borderRadius: 10,
    width: 60,
    alignItems: "center",
  },
  unitText: {
    color: "white",
    fontSize: 18,
    fontWeight: "bold",
  },
  addButton: {
    backgroundColor: "#4CAF50",
    paddingVertical: 15,
    paddingHorizontal: 30,
    borderRadius: 30,
    width: "100%",
    alignItems: "center",
  },
  addButtonText: {
    color: "white",
    fontSize: 18,
    fontWeight: "bold",
  },
});
