import React, {
  createContext,
  useState,
  useContext,
  ReactNode,
  useEffect,
} from "react";
import AsyncStorage from "@react-native-async-storage/async-storage";
import { useUserProfile } from "./UserProfileContext";
import {
  getAllExercises,
  addExercise as addExerciseToSupabase,
  updateExercise as updateExerciseInSupabase,
  deleteExercise as deleteExerciseFromSupabase,
  clearExercisesForDay as clearExercisesForDayFromSupabase,
} from "../services/ExerciseService";

export interface Exercise {
  id: string;
  name: string;
  date: string; // ISO date string YYYY-MM-DD
  duration: number; // in minutes
  caloriesBurned: number;
  type: ExerciseType;
  createdAt?: string;
}

export type ExerciseType =
  | "cardio"
  | "strength"
  | "flexibility"
  | "sports"
  | "other";

export const EXERCISE_TYPES: ExerciseType[] = [
  "cardio",
  "strength",
  "flexibility",
  "sports",
  "other",
];

// MET (Metabolic Equivalent of Task) values for different exercise types
// These are approximate values and can be adjusted
export const EXERCISE_MET: Record<string, number> = {
  // Cardio exercises
  running: 9.8, // Running at 6 mph
  jogging: 7.0, // Jogging at 5 mph
  walking: 3.5, // Walking at 3 mph
  cycling: 8.0, // Cycling at 12-14 mph
  swimming: 7.0, // Swimming laps, moderate effort
  jumping_rope: 10.0, // Jumping rope, moderate pace
  hiking: 5.3, // Hiking, general

  // Strength training
  weight_lifting: 3.5, // Weight lifting, moderate effort
  bodyweight_exercise: 3.8, // Bodyweight exercises
  circuit_training: 8.0, // Circuit training with minimal rest

  // Flexibility exercises
  yoga: 2.5, // Yoga, hatha or general
  stretching: 2.3, // Stretching, general
  pilates: 3.0, // Pilates, general

  // Sports
  basketball: 6.5, // Basketball, general play
  tennis: 7.3, // Tennis, singles
  soccer: 7.0, // Soccer, casual
  volleyball: 4.0, // Volleyball, non-competitive
  golf: 4.8, // Golf, walking and carrying clubs

  // Other common activities
  dancing: 4.5, // Dancing, aerobic or ballet
  gardening: 3.8, // Gardening, general
  housework: 3.0, // Housework, moderate effort
  stair_climbing: 4.0, // Stair climbing, slow pace

  // Default value for unlisted exercises
  other: 4.0,
};

// Common exercises grouped by type
export const COMMON_EXERCISES: Record<ExerciseType, string[]> = {
  cardio: [
    "running",
    "jogging",
    "walking",
    "cycling",
    "swimming",
    "jumping_rope",
    "hiking",
  ],
  strength: ["weight_lifting", "bodyweight_exercise", "circuit_training"],
  flexibility: ["yoga", "stretching", "pilates"],
  sports: ["basketball", "tennis", "soccer", "volleyball", "golf"],
  other: ["dancing", "gardening", "housework", "stair_climbing", "other"],
};

interface ExerciseContextType {
  exercises: Record<string, Exercise[]>; // Keyed by date
  todayExercises: Exercise[];
  addExercise: (exercise: Omit<Exercise, "id">) => Promise<void>;
  updateExercise: (exercise: Exercise) => Promise<void>;
  deleteExercise: (id: string, date: string) => Promise<void>;
  clearExercisesForDay: (date: string) => Promise<void>;
  calculateDailyCaloriesBurned: (date: string) => number;
  getExercisesByDate: (date: string) => Exercise[];
  calculateCaloriesBurned: (
    weight: number,
    exerciseName: string,
    duration: number
  ) => number;
  isLoading: boolean;
}

export const ExerciseContext = createContext<ExerciseContextType>({
  exercises: {},
  todayExercises: [],
  addExercise: async () => {},
  updateExercise: async () => {},
  deleteExercise: async () => {},
  clearExercisesForDay: async () => {},
  calculateDailyCaloriesBurned: () => 0,
  getExercisesByDate: () => [],
  calculateCaloriesBurned: () => 0,
  isLoading: true,
});

interface ExerciseProviderProps {
  children: ReactNode;
}

export function ExerciseProvider({ children }: ExerciseProviderProps) {
  const [exercises, setExercises] = useState<Record<string, Exercise[]>>({});
  const { profile } = useUserProfile();
  const [isLoading, setIsLoading] = useState(true);

  // Get today's date in YYYY-MM-DD format
  const today = new Date().toISOString().split("T")[0];

  // Get today's exercises
  const todayExercises = exercises[today] || [];

  // Load exercises from Supabase on component mount
  useEffect(() => {
    const loadExercises = async () => {
      try {
        setIsLoading(true);

        // Try to load exercises from Supabase
        const supabaseExercises = await getAllExercises();

        if (Object.keys(supabaseExercises).length > 0) {
          setExercises(supabaseExercises);
        } else {
          // Fallback to local storage if no exercises in Supabase
          const storedExercises = await AsyncStorage.getItem("exercises");
          if (storedExercises) {
            const parsedExercises = JSON.parse(storedExercises);
            setExercises(parsedExercises);

            // Migrate each exercise to Supabase
            for (const date in parsedExercises) {
              for (const exercise of parsedExercises[date]) {
                const { id, ...rest } = exercise;
                await addExerciseToSupabase(rest);
              }
            }

            // Clear local storage after migration
            await AsyncStorage.removeItem("exercises");
          }
        }
      } catch (error) {
        console.error("Failed to load exercises:", error);
      } finally {
        setIsLoading(false);
      }
    };

    loadExercises();
  }, []);

  // Calculate calories burned based on MET value, weight, and duration
  const calculateCaloriesBurned = (
    weight: number, // in kg
    exerciseName: string,
    duration: number // in minutes
  ): number => {
    // Formula: Calories = MET × weight in kg × duration in hours
    // MET = Metabolic Equivalent of Task

    // Get MET value for the exercise or use default if not found
    const met = EXERCISE_MET[exerciseName] || EXERCISE_MET["other"];

    // Convert duration from minutes to hours
    const durationInHours = duration / 60;

    // Calculate calories burned
    const caloriesBurned = met * weight * durationInHours;

    return Math.round(caloriesBurned);
  };

  // Add a new exercise
  const addExercise = async (exercise: Omit<Exercise, "id">) => {
    try {
      // Add to Supabase
      const newExercise = await addExerciseToSupabase(exercise);

      if (newExercise) {
        // Update local state
        setExercises((prevExercises) => {
          const updatedExercises = { ...prevExercises };

          if (!updatedExercises[exercise.date]) {
            updatedExercises[exercise.date] = [];
          }

          updatedExercises[exercise.date] = [
            ...updatedExercises[exercise.date],
            newExercise,
          ];

          return updatedExercises;
        });
      } else {
        // Fallback to local storage if Supabase fails
        const id = Date.now().toString();
        const localExercise: Exercise = {
          ...exercise,
          id,
        };

        setExercises((prevExercises) => {
          const updatedExercises = { ...prevExercises };

          if (!updatedExercises[exercise.date]) {
            updatedExercises[exercise.date] = [];
          }

          updatedExercises[exercise.date] = [
            ...updatedExercises[exercise.date],
            localExercise,
          ];

          // Save to local storage as fallback
          AsyncStorage.setItem("exercises", JSON.stringify(updatedExercises));

          return updatedExercises;
        });
      }
    } catch (error) {
      console.error("Failed to add exercise:", error);
    }
  };

  // Update an existing exercise
  const updateExercise = async (exercise: Exercise) => {
    try {
      // Update in Supabase
      const success = await updateExerciseInSupabase(exercise);

      // Update local state regardless of Supabase success
      setExercises((prevExercises) => {
        const updatedExercises = { ...prevExercises };

        if (!updatedExercises[exercise.date]) {
          return prevExercises; // No exercises for that date
        }

        updatedExercises[exercise.date] = updatedExercises[exercise.date].map(
          (item) => (item.id === exercise.id ? exercise : item)
        );

        return updatedExercises;
      });
    } catch (error) {
      console.error("Failed to update exercise:", error);
    }
  };

  // Delete an exercise
  const deleteExercise = async (id: string, date: string) => {
    try {
      // Delete from Supabase
      const success = await deleteExerciseFromSupabase(id);

      // Update local state regardless of Supabase success
      setExercises((prevExercises) => {
        const updatedExercises = { ...prevExercises };

        if (!updatedExercises[date]) {
          return prevExercises; // No exercises for that date
        }

        updatedExercises[date] = updatedExercises[date].filter(
          (item) => item.id !== id
        );

        return updatedExercises;
      });
    } catch (error) {
      console.error("Failed to delete exercise:", error);
    }
  };

  // Clear all exercises for a specific day
  const clearExercisesForDay = async (date: string) => {
    try {
      // Clear from Supabase
      const success = await clearExercisesForDayFromSupabase(date);

      // Update local state regardless of Supabase success
      setExercises((prevExercises) => {
        const { [date]: _, ...rest } = prevExercises;
        return rest;
      });
    } catch (error) {
      console.error("Failed to clear exercises for day:", error);
    }
  };

  // Calculate total calories burned for a specific day
  const calculateDailyCaloriesBurned = (date: string): number => {
    const dayExercises = exercises[date] || [];

    return dayExercises.reduce(
      (total, exercise) => total + exercise.caloriesBurned,
      0
    );
  };

  // Get exercises for a specific date
  const getExercisesByDate = (date: string): Exercise[] => {
    return exercises[date] || [];
  };

  return (
    <ExerciseContext.Provider
      value={{
        exercises,
        todayExercises,
        addExercise,
        updateExercise,
        deleteExercise,
        clearExercisesForDay,
        calculateDailyCaloriesBurned,
        getExercisesByDate,
        calculateCaloriesBurned,
        isLoading,
      }}
    >
      {children}
    </ExerciseContext.Provider>
  );
}

export const useExercise = () => useContext(ExerciseContext);
