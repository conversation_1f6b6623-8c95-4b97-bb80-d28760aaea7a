import React from "react";
import { View, StyleSheet } from "react-native";
import { LinearGradient } from "expo-linear-gradient";
import { useTheme } from "@/context/ThemeContext";

// This is a shim for web and Android where the tab bar is generally opaque.
export default function TabBarBackground() {
  const { resolvedTheme } = useTheme();

  // Different gradient colors for light and dark themes
  const gradientColors =
    resolvedTheme === "dark"
      ? (["#111827", "#1F2937"] as const) // Dark theme gradient as tuple
      : (["#fafafa", "#f5f5f5"] as const); // Light theme gradient - minimal gray

  return (
    <View style={StyleSheet.absoluteFill}>
      <LinearGradient colors={gradientColors} style={StyleSheet.absoluteFill} />
    </View>
  );
}

export function useBottomTabOverflow() {
  return 0;
}
