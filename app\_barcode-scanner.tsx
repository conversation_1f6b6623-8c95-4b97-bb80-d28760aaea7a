import React, { useState, useEffect } from "react";
import {
  StyleSheet,
  Text,
  View,
  TouchableOpacity,
  Alert,
  ActivityIndicator,
  SafeAreaView,
} from "react-native";
import { useRouter } from "expo-router";
import { LinearGradient } from "expo-linear-gradient";
import { useFoodLog } from "../context/FoodLogContext";
import { Ionicons } from "@expo/vector-icons";
import * as Haptics from "expo-haptics";
import { CameraView, useCameraPermissions } from "expo-camera";

export default function BarcodeScannerScreen() {
  const [hasPermission, setHasPermission] = useState<boolean | null>(null);
  const [scanned, setScanned] = useState(false);
  const [loading, setLoading] = useState(false);
  const [torchOn, setTorchOn] = useState(false);
  const router = useRouter();
  const { addFoodItem } = useFoodLog();
  const [permission, requestPermission] = useCameraPermissions();

  useEffect(() => {
    requestPermission();
  }, []);

  useEffect(() => {
    if (permission) {
      setHasPermission(permission.granted);
    }
  }, [permission]);

  // Toggle torch function
  const toggleTorch = () => {
    setTorchOn(!torchOn);
  };

  const handleBarCodeScanned = (scanningResult: {
    type: string;
    data: string;
  }) => {
    try {
      if (scanned) return;

      const { data, type } = scanningResult;
      setScanned(true);
      setLoading(true);

      // Provide haptic feedback
      Haptics.notificationAsync(Haptics.NotificationFeedbackType.Success);

      console.log(
        `Bar code with type ${type} and data ${data} has been scanned!`
      );

      // Here we would typically make an API call to a food database
      // For demonstration, we'll use mock data
      mockFoodDatabaseLookup(data)
        .then((foodItem) => {
          if (foodItem) {
            // Add the food item to the log
            addFoodItem({
              ...foodItem,
              mealType: "snack", // Default meal type
              timestamp: new Date().toISOString(),
            });

            // Alert the user that the food has been added
            Alert.alert(
              "Food Added",
              `${foodItem.name} has been added to your food log.`,
              [
                {
                  text: "View Food Log",
                  onPress: () => {
                    // Use setTimeout to ensure that component is fully mounted
                    setTimeout(() => {
                      router.navigate("/(tabs)");
                    }, 0);
                  },
                },
                {
                  text: "Scan Another",
                  onPress: () => {
                    setScanned(false);
                    setLoading(false);
                  },
                },
              ]
            );
          } else {
            Alert.alert(
              "Product Not Found",
              "Sorry, we couldn't find this product in our database.",
              [
                {
                  text: "Try Again",
                  onPress: () => {
                    setScanned(false);
                    setLoading(false);
                  },
                },
              ]
            );
          }
        })
        .catch((error) => {
          console.error("Error processing barcode:", error);
          Alert.alert(
            "Error",
            "There was a problem processing the barcode. Please try again.",
            [
              {
                text: "OK",
                onPress: () => {
                  setScanned(false);
                  setLoading(false);
                },
              },
            ]
          );
        })
        .finally(() => {
          setLoading(false);
        });
    } catch (error) {
      console.error("Error in handleBarCodeScanned:", error);
      setScanned(false);
      setLoading(false);
    }
  };

  // Mock function to simulate looking up a food item from a barcode
  const mockFoodDatabaseLookup = async (barcode: string): Promise<any> => {
    // Simulate network delay
    await new Promise((resolve) => setTimeout(resolve, 1000));

    // Return a demo product for barcodes ending in "123"
    if (barcode.endsWith("123")) {
      return {
        id: `food-${barcode}`,
        name: "Demo Protein Bar",
        calories: 200,
        carbs: 20,
        protein: 15,
        fat: 8,
        fiber: 5,
        sugar: 2,
        sodium: 50,
        servingSize: "1 bar (50g)",
        servingSizeUnit: "g",
        servingSizeQuantity: 50,
        brand: "Demo Foods",
        imageUrl: null,
      };
    }

    // Return a different product for barcodes ending in "456"
    if (barcode.endsWith("456")) {
      return {
        id: `food-${barcode}`,
        name: "Demo Yogurt",
        calories: 150,
        carbs: 25,
        protein: 12,
        fat: 3,
        fiber: 0,
        sugar: 20,
        sodium: 80,
        servingSize: "1 cup (245g)",
        servingSizeUnit: "g",
        servingSizeQuantity: 245,
        brand: "Demo Dairy",
        imageUrl: null,
      };
    }

    // Return null for any other barcode to simulate a product not found
    return null;
  };

  if (hasPermission === null) {
    return (
      <SafeAreaView style={styles.loadingContainer}>
        <ActivityIndicator size="large" color="#4CAF50" />
        <Text style={styles.loadingText}>Requesting camera permission...</Text>
      </SafeAreaView>
    );
  }

  if (hasPermission === false) {
    return (
      <SafeAreaView style={styles.loadingContainer}>
        <Ionicons name="camera-outline" size={50} color="#FF5252" />
        <Text style={styles.errorText}>No access to camera</Text>
        <TouchableOpacity
          style={styles.button}
          onPress={() => {
            // Use setTimeout to ensure navigation occurs after component mounting
            setTimeout(() => {
              router.back();
            }, 0);
          }}
        >
          <Text style={styles.buttonText}>Go Back</Text>
        </TouchableOpacity>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={styles.container}>
      <LinearGradient
        colors={["rgba(0,0,0,0.8)", "transparent"]}
        style={styles.headerGradient}
      />

      <CameraView
        style={styles.scanner}
        onBarcodeScanned={scanned ? undefined : handleBarCodeScanned}
        barcodeScannerSettings={{
          barcodeTypes: [
            "qr",
            "ean13",
            "ean8",
            "code128",
            "code39",
            "code93",
            "upc_a",
            "upc_e",
            "pdf417",
            "aztec",
            "datamatrix",
            "itf14",
          ],
        }}
        enableTorch={torchOn}
      />

      <View style={styles.overlay}>
        <View style={styles.scanWindow} />
      </View>

      <View style={styles.buttonsContainer}>
        <TouchableOpacity
          style={[styles.iconButton, torchOn && styles.iconButtonActive]}
          onPress={toggleTorch}
        >
          <Ionicons
            name={torchOn ? "flash" : "flash-outline"}
            size={24}
            color={torchOn ? "#FFD700" : "white"}
          />
        </TouchableOpacity>

        {scanned && (
          <TouchableOpacity
            style={styles.scanButton}
            onPress={() => setScanned(false)}
            disabled={loading}
          >
            {loading ? (
              <ActivityIndicator color="white" />
            ) : (
              <Text style={styles.scanButtonText}>Tap to Scan Again</Text>
            )}
          </TouchableOpacity>
        )}

        <TouchableOpacity
          style={styles.iconButton}
          onPress={() => {
            // Use setTimeout to ensure navigation occurs after component mounting
            setTimeout(() => {
              router.back();
            }, 0);
          }}
        >
          <Ionicons name="close-outline" size={24} color="white" />
        </TouchableOpacity>
      </View>

      <View style={styles.instructionsContainer}>
        <Text style={styles.instructionsText}>
          Align the barcode within the frame to scan
        </Text>
      </View>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: "#000",
  },
  scanner: {
    ...StyleSheet.absoluteFillObject,
  },
  overlay: {
    ...StyleSheet.absoluteFillObject,
    justifyContent: "center",
    alignItems: "center",
    backgroundColor: "rgba(0, 0, 0, 0.5)",
  },
  scanWindow: {
    width: 250,
    height: 250,
    borderWidth: 2,
    borderColor: "#4CAF50",
    backgroundColor: "transparent",
    borderRadius: 10,
  },
  buttonsContainer: {
    position: "absolute",
    bottom: 40,
    left: 0,
    right: 0,
    flexDirection: "row",
    justifyContent: "space-around",
    alignItems: "center",
    paddingHorizontal: 20,
  },
  scanButton: {
    backgroundColor: "#4CAF50",
    paddingVertical: 15,
    paddingHorizontal: 30,
    borderRadius: 30,
    marginHorizontal: 10,
  },
  scanButtonText: {
    color: "white",
    fontSize: 16,
    fontWeight: "bold",
  },
  iconButton: {
    backgroundColor: "rgba(0, 0, 0, 0.5)",
    width: 50,
    height: 50,
    borderRadius: 25,
    justifyContent: "center",
    alignItems: "center",
    borderWidth: 1,
    borderColor: "rgba(255, 255, 255, 0.3)",
  },
  iconButtonActive: {
    backgroundColor: "rgba(0, 0, 0, 0.8)",
    borderColor: "#FFD700",
  },
  instructionsContainer: {
    position: "absolute",
    top: 100,
    left: 0,
    right: 0,
    alignItems: "center",
  },
  instructionsText: {
    color: "white",
    fontSize: 16,
    backgroundColor: "rgba(0, 0, 0, 0.7)",
    paddingVertical: 8,
    paddingHorizontal: 16,
    borderRadius: 20,
    overflow: "hidden",
  },
  headerGradient: {
    position: "absolute",
    left: 0,
    right: 0,
    top: 0,
    height: 100,
    zIndex: 1,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
    backgroundColor: "#f5f5f5",
  },
  loadingText: {
    marginTop: 20,
    fontSize: 16,
    color: "#333",
  },
  errorText: {
    marginTop: 20,
    fontSize: 18,
    color: "#FF5252",
    marginBottom: 20,
  },
  button: {
    backgroundColor: "#4CAF50",
    paddingVertical: 12,
    paddingHorizontal: 30,
    borderRadius: 30,
    marginTop: 20,
  },
  buttonText: {
    color: "white",
    fontSize: 16,
    fontWeight: "bold",
  },
});
