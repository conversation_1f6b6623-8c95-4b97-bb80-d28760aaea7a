import AsyncStorage from "@react-native-async-storage/async-storage";
import "react-native-url-polyfill/auto";
import { createClient } from "@supabase/supabase-js";
import * as Device from "expo-device";
import * as SecureStore from "expo-secure-store";

// Supabase configuration
const supabaseUrl = "https://awwwqyzndgsxkcplkwki.supabase.co";
const supabaseAnonKey =
  "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImF3d3dxeXpuZGdzeGtjcGxrd2tpIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDI2NjIzOTIsImV4cCI6MjA1ODIzODM5Mn0.uOA5irjBkwueqzml0W2GtTApseRZHzlOo4fP2Zb8EtU";

// Keys for storing tokens
const REFRESH_TOKEN_KEY = "refresh_token";
const ACCESS_TOKEN_KEY = "access_token";

// Store tokens securely
export const storeSessionTokens = async (
  access_token: string,
  refresh_token: string
) => {
  try {
    await SecureStore.setItemAsync(REFRESH_TOKEN_KEY, refresh_token);
    await SecureStore.setItemAsync(ACCESS_TOKEN_KEY, access_token);
  } catch (error) {
    console.error("Failed to store session tokens:", error);
  }
};

// Retrieve tokens securely
export const getSessionTokens = async (): Promise<{
  access_token: string | null;
  refresh_token: string | null;
}> => {
  try {
    const refresh_token = await SecureStore.getItemAsync(REFRESH_TOKEN_KEY);
    const access_token = await SecureStore.getItemAsync(ACCESS_TOKEN_KEY);
    return { access_token, refresh_token };
  } catch (error) {
    console.error("Failed to get session tokens:", error);
    return { access_token: null, refresh_token: null };
  }
};

// Remove tokens
export const clearSessionTokens = async () => {
  try {
    await SecureStore.deleteItemAsync(REFRESH_TOKEN_KEY);
    await SecureStore.deleteItemAsync(ACCESS_TOKEN_KEY);
  } catch (error) {
    console.error("Failed to clear session tokens:", error);
  }
};

// Initialize the Supabase client
export const supabase = createClient(supabaseUrl, supabaseAnonKey, {
  auth: {
    storage: AsyncStorage,
    autoRefreshToken: true,
    persistSession: true,
    detectSessionInUrl: false,
  },
});

// --- Robust Token Management ---
let isRefreshing = false;
let refreshSubscribers: ((accessToken: string | null) => void)[] = [];

// Function to add subscribers to the refresh queue
const subscribeTokenRefresh = (cb: (accessToken: string | null) => void) => {
  refreshSubscribers.push(cb);
};

// Function to notify all subscribers
const onRefreshed = (accessToken: string | null) => {
  refreshSubscribers.forEach((cb) => cb(accessToken));
  // Clear subscribers after notifying
  refreshSubscribers = [];
};

// Listen to auth state changes for token refreshes and session persistence
supabase.auth.onAuthStateChange(async (event, session) => {
  console.log("Supabase Auth Event:", event, !!session);
  if (event === "TOKEN_REFRESHED" || event === "SIGNED_IN") {
    if (session?.access_token && session?.refresh_token) {
      console.log("Auth Listener: Storing new tokens from event:", event);
      await storeSessionTokens(session.access_token, session.refresh_token);
      if (event === "TOKEN_REFRESHED") {
        onRefreshed(session.access_token);
      }
    } else if (!session) {
      // This case might happen if a refresh fails definitively
      console.log(
        "Auth Listener: No session on TOKEN_REFRESHED/SIGNED_IN, clearing tokens."
      );
      await clearSessionTokens();
      onRefreshed(null);
    }
  } else if (event === "SIGNED_OUT") {
    console.log("Auth Listener: SIGNED_OUT, clearing tokens.");
    await clearSessionTokens();
    onRefreshed(null);
  } else if (event === "INITIAL_SESSION") {
    // This event fires when the session is initially loaded from storage (or not)
    // If session exists, tokens would have been loaded by the client. If not, they are null.
    // We can ensure our SecureStore is in sync if needed, but autoRefreshToken should handle it.
    if (session?.access_token && session?.refresh_token) {
      console.log(
        "Auth Listener: INITIAL_SESSION with session, ensuring tokens are stored."
      );
      await storeSessionTokens(session.access_token, session.refresh_token);
    } else {
      console.log("Auth Listener: INITIAL_SESSION without session.");
      // Potentially clear tokens if they were expected but not found,
      // though supabase client persistSession should handle this.
      // Consider if clearSessionTokens() is needed here if session is null.
    }
  }
});

let restoreSessionPromise: Promise<any> | null = null;

// Restore session on app launch
export const restoreSessionFromTokens = async () => {
  if (restoreSessionPromise) {
    console.log(
      "Restore session already in progress, returning existing promise."
    );
    return restoreSessionPromise;
  }

  restoreSessionPromise = (async () => {
    try {
      console.log("Attempting to restore session from SecureStore...");
      const { access_token, refresh_token } = await getSessionTokens();

      if (access_token && refresh_token) {
        console.log("Found tokens in SecureStore, attempting to set session.");
        // Set isRefreshing true before calling setSession, as setSession itself can trigger a refresh
        isRefreshing = true;
        const { data, error } = await supabase.auth.setSession({
          access_token,
          refresh_token,
        });
        isRefreshing = false;

        if (error) {
          console.error(
            "Failed to restore session from tokens (setSession error):",
            error.message
          );
          // If setSession fails (e.g. refresh token already used or invalid),
          // clear the bad tokens and sign out the user on the client.
          await clearSessionTokens();
          // Potentially trigger a sign-out flow in your app here if needed
          // await supabase.auth.signOut(); // This would trigger SIGNED_OUT event
          return null;
        }
        console.log("Session successfully set/restored from tokens.");
        // The onAuthStateChange listener should have already stored the new tokens if they changed.
        return data.session;
      }
      console.log("No tokens found in SecureStore to restore session.");
      return null;
    } catch (e) {
      console.error("Exception during restoreSessionFromTokens:", e);
      isRefreshing = false; // Ensure this is reset on error
      return null;
    } finally {
      restoreSessionPromise = null; // Clear the promise once done
    }
  })();
  return restoreSessionPromise;
};

/**
 * A wrapper for Supabase client methods that require an access token.
 * This will handle automatic token refresh if needed.
 */
export const callSupabase = async <T>(
  supabaseCall: (accessToken: string) => Promise<T>
): Promise<T> => {
  try {
    let {
      data: { session },
    } = await supabase.auth.getSession();

    if (!session || !session.access_token) {
      console.log(
        "callSupabase: No session or access token, attempting to refresh or restore..."
      );
      const restoredSession = await restoreSessionFromTokens();
      if (restoredSession?.access_token) {
        console.log(
          "callSupabase: Session restored/refreshed, using new access token."
        );
        // Ensure the session variable used for the call is updated
        return await supabaseCall(restoredSession.access_token);
      } else {
        console.error(
          "callSupabase: Unable to get a valid session after attempt to restore/refresh."
        );
        throw new Error(
          "Authentication required: Unable to get a valid session."
        );
      }
    }
    // If we reach here, the initial session was valid.
    return await supabaseCall(session.access_token);
  } catch (error: any) {
    if (
      error.message?.includes("Authentication required") ||
      error.message?.includes("AuthApiError") ||
      error.message?.includes("JWT expired")
    ) {
      console.warn(
        "callSupabase: Auth error or JWT expired, attempting explicit refresh. Error:",
        error.message
      );
      if (isRefreshing) {
        console.log(
          "callSupabase: Refresh already in progress, subscribing..."
        );
        return new Promise((resolve, reject) => {
          subscribeTokenRefresh(async (newAccessToken) => {
            if (newAccessToken) {
              console.log(
                "callSupabase: Refresh successful via subscriber, retrying call."
              );
              try {
                resolve(await supabaseCall(newAccessToken));
              } catch (e) {
                reject(e);
              }
            } else {
              console.error("callSupabase: Refresh failed via subscriber.");
              reject(new Error("Token refresh failed"));
            }
          });
        });
      }

      isRefreshing = true;
      try {
        const { data, error: refreshError } =
          await supabase.auth.refreshSession();
        if (refreshError) {
          console.error("callSupabase: refreshSession failed:", refreshError);
          await clearSessionTokens(); // Clear potentially bad tokens
          // await supabase.auth.signOut(); // Force sign out, triggers onAuthStateChange
          onRefreshed(null);
          isRefreshing = false;
          throw refreshError; // Propagate error
        }
        if (!data.session?.access_token) {
          console.error(
            "callSupabase: refreshSession succeeded but no access token."
          );
          await clearSessionTokens();
          // await supabase.auth.signOut();
          onRefreshed(null);
          isRefreshing = false;
          throw new Error("Refresh succeeded but no access token found.");
        }
        console.log("callSupabase: refreshSession successful, retrying call.");
        // onAuthStateChange should store the new tokens.
        onRefreshed(data.session.access_token);
        isRefreshing = false;
        return await supabaseCall(data.session.access_token);
      } catch (e) {
        isRefreshing = false; // Ensure this is reset on error
        onRefreshed(null); // Notify subscribers of failure
        throw e;
      }
    }
    throw error; // Re-throw other errors
  }
};

// Sign up or sign in (anonymous or with credentials)
export const signUpOrSignIn = async (options: {
  email?: string;
  password?: string;
  anonymous?: boolean;
}) => {
  try {
    let result;
    if (options.anonymous) {
      // Supabase does not have built-in anonymous, so generate a random email/password
      const anonEmail = `anon-${Date.now()}-${Math.random()
        .toString(36)
        .substring(2, 9)}@anon.user`;
      const anonPassword = Math.random().toString(36).substring(2, 15);

      console.log("Signing up anonymously");
      result = await supabase.auth.signUp({
        email: anonEmail,
        password: anonPassword,
      });
    } else if (options.email && options.password) {
      // Try sign in first
      console.log("Attempting to sign in with credentials");
      result = await supabase.auth.signInWithPassword({
        email: options.email,
        password: options.password,
      });

      if (result.error) {
        console.log("Sign in failed, trying sign up");
        // If sign in fails, try sign up
        result = await supabase.auth.signUp({
          email: options.email,
          password: options.password,
        });
      }
    } else {
      throw new Error("Must provide either anonymous or email/password");
    }

    console.log("Auth result:", result.error ? "Error" : "Success");

    if (result.error) {
      console.error("Authentication error:", result.error.message);
      return result;
    }

    if (
      result.data?.session?.refresh_token &&
      result.data?.session?.access_token
    ) {
      await storeSessionTokens(
        result.data.session.access_token,
        result.data.session.refresh_token
      );
      console.log("Stored session tokens");
    }

    return result;
  } catch (error) {
    console.error("Authentication exception:", error);
    return { data: null, error };
  }
};

// Ensure user is authenticated - sign up anonymously if needed
export const ensureAuthenticated = async (): Promise<boolean> => {
  try {
    // First try to restore existing session
    const session = await restoreSessionFromTokens();
    if (session) {
      console.log("Restored existing session");
      return true;
    }

    // If no session, check if user exists
    const {
      data: { user },
    } = await supabase.auth.getUser();
    if (user) {
      console.log("User exists:", user.id);
      return true;
    }

    // No session or user, sign up anonymously
    console.log("No existing user, signing up anonymously");
    const result = await signUpOrSignIn({ anonymous: true });
    return !result.error;
  } catch (error) {
    console.error("Error ensuring authentication:", error);
    return false;
  }
};

// Get the current user ID (useful for data operations)
export const getCurrentUserId = async (): Promise<string | null> => {
  try {
    const {
      data: { user },
    } = await supabase.auth.getUser();
    return user?.id || null;
  } catch (error) {
    console.error("Failed to get current user ID:", error);
    return null;
  }
};
