import { useTheme } from "@/context/ThemeContext";
import { Ionicons } from "@expo/vector-icons";
import * as Haptics from "expo-haptics";
import { LinearGradient } from "expo-linear-gradient";
import React, { useEffect, useRef } from "react";
import { Animated, Modal, Text, TouchableOpacity, View } from "react-native";

// Props for the modal
export interface AddItemModalProps {
  isVisible: boolean;
  onClose: () => void;
  onSelectTakePhoto: () => void;
  onSelectUploadPhoto: () => void;
  onSelectFood: () => void;
  onSelectWorkout: () => void;
}

export function AddItemModal({
  isVisible,
  onClose,
  onSelectTakePhoto,
  onSelectUploadPhoto,
  onSelectFood,
  onSelectWorkout,
}: AddItemModalProps) {
  const { resolvedTheme } = useTheme();

  // Animation values
  const fadeAnim = useRef(new Animated.Value(0)).current;
  const slideAnim = useRef(new Animated.Value(100)).current;

  // Run animations when visibility changes
  useEffect(() => {
    if (isVisible) {
      // Start animations when modal becomes visible
      Animated.parallel([
        Animated.timing(fadeAnim, {
          toValue: 1,
          duration: 300,
          useNativeDriver: true,
        }),
        Animated.timing(slideAnim, {
          toValue: 0,
          duration: 300,
          useNativeDriver: true,
        }),
      ]).start();
    } else {
      // Reset animation values when modal is hidden
      fadeAnim.setValue(0);
      slideAnim.setValue(100);
    }
  }, [isVisible, fadeAnim, slideAnim]);

  // Simple direct handlers for each action
  const handleTakePhoto = () => {
    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
    onSelectTakePhoto();
  };

  const handleUploadPhoto = () => {
    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
    onSelectUploadPhoto();
  };

  const handleFood = () => {
    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
    onSelectFood();
  };

  const handleWorkout = () => {
    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
    onSelectWorkout();
  };

  const handleCancel = () => {
    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
    onClose();
  };

  return (
    <Modal
      visible={isVisible}
      transparent={true}
      animationType="none" // Disable built-in animation to use our custom one
      onRequestClose={handleCancel}
    >
      <Animated.View
        className="flex-1 justify-end"
        style={{
          backgroundColor: "rgba(0, 0, 0, 0.6)",
          opacity: fadeAnim,
        }}
      >
        <Animated.View
          className="w-full px-4 pb-6"
          style={{
            transform: [{ translateY: slideAnim }],
          }}
        >
          {/* Main Content Container */}
          <View className="rounded-3xl shadow-2xl overflow-hidden bg-white dark:bg-gray-800 ">
            {/* Header */}
            <LinearGradient
              colors={
                resolvedTheme === "dark"
                  ? ["#1f2937", "#374151"]
                  : ["#f8fafc", "#e2e8f0", "#f1f5f9"]
              }
              className="px-6 py-5"
              start={{ x: 0, y: 0 }}
              end={{ x: 1, y: 1 }}
            >
              <Text className="text-2xl font-extrabold dark:text-white text-gray-900 shadow-sm">
                Add to Your Day
              </Text>
              <Text className="mt-1 dark:text-gray-300 text-gray-600">
                What would you like to track?
              </Text>
            </LinearGradient>

            {/* Options List */}
            <View>
              {/* Take Photo Option */}
              <TouchableOpacity
                className="flex-row items-center px-6 py-5 border-b border-gray-200 dark:border-gray-700"
                onPress={handleTakePhoto}
                activeOpacity={0.6}
              >
                <View className="w-16 h-16 rounded-3xl items-center justify-center shadow-sm bg-gray-100 dark:bg-gray-700">
                  <Text className="text-3xl text-gray-700 dark:text-gray-200">
                    📸
                  </Text>
                </View>
                <View className="flex-1 ml-5">
                  <Text className="text-lg font-bold mb-1 text-gray-900 dark:text-gray-100">
                    Take Photo
                  </Text>
                  <Text className="text-sm text-gray-600 dark:text-gray-400">
                    Analyze nutrition from a photo
                  </Text>
                </View>
                <View className="w-8 h-8 rounded-full items-center justify-center bg-gray-100 dark:bg-gray-700">
                  <Ionicons
                    name="add"
                    size={20}
                    color={resolvedTheme === "light" ? "#374151" : "#e5e7eb"}
                  />
                </View>
              </TouchableOpacity>

              {/* Upload Photo Option */}
              <TouchableOpacity
                className="flex-row items-center px-6 py-5 border-b border-gray-200 dark:border-gray-700"
                onPress={handleUploadPhoto}
                activeOpacity={0.6}
              >
                <View className="w-16 h-16 rounded-3xl items-center justify-center shadow-sm bg-gray-100 dark:bg-gray-700">
                  <Text className="text-3xl text-gray-700 dark:text-gray-200">
                    🖼️
                  </Text>
                </View>
                <View className="flex-1 ml-5">
                  <Text className="text-lg font-bold mb-1 text-gray-900 dark:text-gray-100">
                    Upload Photo
                  </Text>
                  <Text className="text-sm text-gray-600 dark:text-gray-400">
                    Analyze nutrition from an image
                  </Text>
                </View>
                <View className="w-8 h-8 rounded-full items-center justify-center bg-gray-100 dark:bg-gray-700">
                  <Ionicons
                    name="add"
                    size={20}
                    color={resolvedTheme === "light" ? "#374151" : "#e5e7eb"}
                  />
                </View>
              </TouchableOpacity>

              {/* Log Food Option */}
              <TouchableOpacity
                className="flex-row items-center px-6 py-5 border-b border-gray-200 dark:border-gray-700"
                onPress={handleFood}
                activeOpacity={0.6}
              >
                <View className="w-16 h-16 rounded-3xl items-center justify-center shadow-sm bg-gray-100 dark:bg-gray-700">
                  <Text className="text-3xl text-gray-700 dark:text-gray-200">
                    🍎
                  </Text>
                </View>
                <View className="flex-1 ml-5">
                  <Text className="text-lg font-bold mb-1 text-gray-900 dark:text-gray-100">
                    Log Food
                  </Text>
                  <Text className="text-sm text-gray-600 dark:text-gray-400">
                    Add a meal or a snack
                  </Text>
                </View>
                <View className="w-8 h-8 rounded-full items-center justify-center bg-gray-100 dark:bg-gray-700">
                  <Ionicons
                    name="add"
                    size={20}
                    color={resolvedTheme === "light" ? "#374151" : "#e5e7eb"}
                  />
                </View>
              </TouchableOpacity>

              {/* Log Workout Option */}
              <TouchableOpacity
                className="flex-row items-center px-6 py-5"
                onPress={handleWorkout}
                activeOpacity={0.6}
              >
                <View className="w-16 h-16 rounded-3xl items-center justify-center shadow-sm bg-gray-100 dark:bg-gray-700">
                  <Text className="text-3xl text-gray-700 dark:text-gray-200">
                    🏋️
                  </Text>
                </View>
                <View className="flex-1 ml-5">
                  <Text className="text-lg font-bold mb-1 text-gray-900 dark:text-gray-100">
                    Log Workout
                  </Text>
                  <Text className="text-sm text-gray-600 dark:text-gray-400">
                    Add a physical activity
                  </Text>
                </View>
                <View className="w-8 h-8 rounded-full items-center justify-center bg-gray-100 dark:bg-gray-700">
                  <Ionicons
                    name="add"
                    size={20}
                    color={resolvedTheme === "light" ? "#374151" : "#e5e7eb"}
                  />
                </View>
              </TouchableOpacity>
            </View>
          </View>

          {/* Cancel Button */}
          <View className="mt-4 rounded-2xl shadow-lg overflow-hidden bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700">
            <TouchableOpacity
              className="py-4"
              onPress={handleCancel}
              activeOpacity={0.6}
            >
              <Text className="text-center font-semibold text-base text-gray-900 dark:text-gray-100">
                Cancel
              </Text>
            </TouchableOpacity>
          </View>
        </Animated.View>
      </Animated.View>
    </Modal>
  );
}
