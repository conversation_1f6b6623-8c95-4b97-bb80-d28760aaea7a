import React from "react";
import { View, Text, TouchableOpacity, Alert } from "react-native";
import { Ionicons } from "@expo/vector-icons";
import * as Haptics from "expo-haptics";
import { useRouter } from "expo-router";

import { useExercise, ExerciseType } from "@/context/ExerciseContext";

interface DailyExerciseLogProps {
  date: string;
}

export function DailyExerciseLog({ date }: DailyExerciseLogProps) {
  const router = useRouter();
  const { getExercisesByDate, deleteExercise, calculateDailyCaloriesBurned } =
    useExercise();

  const exercises = getExercisesByDate(date);
  const totalCaloriesBurned = calculateDailyCaloriesBurned(date);

  // Group exercises by type
  const exercisesByType = exercises.reduce((acc, exercise) => {
    if (!acc[exercise.type]) {
      acc[exercise.type] = [];
    }
    acc[exercise.type].push(exercise);
    return acc;
  }, {} as Record<ExerciseType, typeof exercises>);

  // Get type names in a specific order
  const typeOrder: ExerciseType[] = [
    "cardio",
    "strength",
    "flexibility",
    "sports",
    "other",
  ];
  const sortedTypes = Object.keys(exercisesByType).sort(
    (a, b) =>
      typeOrder.indexOf(a as ExerciseType) -
      typeOrder.indexOf(b as ExerciseType)
  ) as ExerciseType[];

  const navigateToAddExercise = () => {
    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
    router.push("../add-exercise");
  };

  const handleDeleteExercise = (id: string) => {
    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);

    Alert.alert(
      "Delete Exercise",
      "Are you sure you want to delete this exercise?",
      [
        { text: "Cancel", style: "cancel" },
        {
          text: "Delete",
          style: "destructive",
          onPress: () => deleteExercise(id, date),
        },
      ]
    );
  };

  const getExerciseTypeIcon = (type: ExerciseType) => {
    switch (type) {
      case "cardio":
        return "fitness";
      case "strength":
        return "barbell";
      case "flexibility":
        return "body";
      case "sports":
        return "basketball";
      default:
        return "bicycle";
    }
  };

  if (exercises.length === 0) {
    return (
      <View className="bg-white/5 rounded-3xl p-6 shadow-lg mb-8">
        <View className="flex-row justify-between items-center mb-4">
          <Text className="text-xl font-bold text-white">Exercise Log</Text>
          <TouchableOpacity
            onPress={navigateToAddExercise}
            className="bg-white/10 p-2 rounded-full"
          >
            <Ionicons name="add" size={20} color="white" />
          </TouchableOpacity>
        </View>

        <View className="items-center justify-center py-8">
          <Ionicons name="fitness" size={64} color="#666" />
          <Text className="text-gray-400 text-center mt-4">
            No exercises logged for today. Start tracking your workouts!
          </Text>

          <TouchableOpacity
            onPress={navigateToAddExercise}
            className="bg-gradient-to-r from-purple-500 to-purple-600 px-6 py-3 rounded-full mt-6"
          >
            <Text className="text-white font-semibold text-center">
              Log Exercise
            </Text>
          </TouchableOpacity>
        </View>
      </View>
    );
  }

  return (
    <View className="bg-white/5 rounded-3xl p-6 shadow-lg mb-8">
      <View className="flex-row justify-between items-center mb-4">
        <Text className="text-xl font-bold text-white">Exercise Log</Text>
        <TouchableOpacity
          onPress={navigateToAddExercise}
          className="bg-white/10 p-2 rounded-full"
        >
          <Ionicons name="add" size={20} color="white" />
        </TouchableOpacity>
      </View>

      {sortedTypes.map((type) => (
        <View key={type} className="mb-8">
          <View className="flex-row items-center mb-3">
            <View className="w-10 h-10 bg-purple-500/20 rounded-full items-center justify-center mr-3">
              <Ionicons
                name={getExerciseTypeIcon(type)}
                size={18}
                color="#d8b4fe"
              />
            </View>
            <Text className="text-white font-semibold text-lg capitalize">
              {type}
            </Text>
          </View>

          <View className="space-y-2">
            {exercisesByType[type].map((exercise) => (
              <View
                key={exercise.id}
                className="flex-row justify-between items-center bg-white/10 p-4 rounded-xl mb-3"
              >
                <View>
                  <Text className="text-white capitalize font-medium">
                    {exercise.name.replace(/_/g, " ")}
                  </Text>
                  <Text className="text-gray-400 text-sm mt-1">
                    {exercise.duration} min
                  </Text>
                </View>

                <View className="flex-row items-center">
                  <View className="flex-row items-center mr-3 bg-red-500/10 px-2 py-1 rounded-lg">
                    <Ionicons name="flame" size={16} color="#FF6B6B" />
                    <Text className="text-white ml-1 font-medium">
                      {exercise.caloriesBurned}
                    </Text>
                  </View>

                  <TouchableOpacity
                    onPress={() => handleDeleteExercise(exercise.id)}
                    className="p-2"
                  >
                    <Ionicons name="close-circle" size={18} color="#FF6B6B" />
                  </TouchableOpacity>
                </View>
              </View>
            ))}
          </View>
        </View>
      ))}

      <View className="mt-6 pt-5 border-t border-white/10 flex-row justify-between items-center">
        <Text className="text-white font-medium text-base">
          Total Calories Burned
        </Text>
        <View className="bg-red-500/10 px-4 py-2 rounded-xl flex-row items-center">
          <Ionicons name="flame" size={22} color="#FF6B6B" />
          <Text className="text-2xl font-bold text-white ml-2">
            {totalCaloriesBurned}
          </Text>
        </View>
      </View>

      <TouchableOpacity
        onPress={navigateToAddExercise}
        className="bg-gradient-to-r from-purple-500 to-purple-600 px-6 py-3 rounded-full mt-6"
      >
        <Text className="text-white font-semibold text-center">
          Add More Exercise
        </Text>
      </TouchableOpacity>
    </View>
  );
}
