import { useBottomTabBarHeight } from "@react-navigation/bottom-tabs";
import { StyleSheet, View } from "react-native";
import { useSafeAreaInsets } from "react-native-safe-area-context";
import { LinearGradient } from "expo-linear-gradient";
import { useTheme } from "@/context/ThemeContext";

export default function TabBarBackground() {
  const { resolvedTheme } = useTheme();

  // Different gradient colors for light and dark themes
  const gradientColors =
    resolvedTheme === "dark"
      ? (["#111827", "#1F2937"] as const) // Dark theme gradient
      : (["#f0f4f8", "#e2e8f0"] as const); // Light theme gradient - sophisticated, muted

  return (
    <View style={StyleSheet.absoluteFill}>
      <LinearGradient colors={gradientColors} style={StyleSheet.absoluteFill} />
    </View>
  );
}

export function useBottomTabOverflow() {
  const tabHeight = useBottomTabBarHeight();
  const { bottom } = useSafeAreaInsets();
  return tabHeight - bottom;
}
