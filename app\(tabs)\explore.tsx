import React, { useState, useEffect } from "react";
import {
  View,
  Text,
  ScrollView,
  TouchableOpacity,
  ActivityIndicator,
  Image,
  Animated,
  Linking,
} from "react-native";
import { LinearGradient } from "expo-linear-gradient";
import { Ionicons } from "@expo/vector-icons";
import { BlurView } from "expo-blur";
import * as Haptics from "expo-haptics";

import { useUserProfile } from "@/context/UserProfileContext";

interface Recipe {
  id: string;
  title: string;
  image: string;
  calories: number;
  protein: number;
  carbs: number;
  fat: number;
  prepTime: string;
  healthScore: number;
  url: string;
  tags: string[];
}

// Mock recipe data - in a real app, this would come from an API
const mockRecipes: Recipe[] = [
  {
    id: "1",
    title: "Protein-Packed Greek Yogurt Bowl",
    image:
      "https://images.unsplash.com/photo-1511690656952-34342bb7c2f2?ixlib=rb-1.2.1&auto=format&fit=crop&w=800&q=80",
    calories: 320,
    protein: 24,
    carbs: 32,
    fat: 12,
    prepTime: "5 min",
    healthScore: 9,
    url: "https://www.eatingwell.com/recipe/276004/greek-yogurt-with-fruit-nuts/",
    tags: ["breakfast", "high-protein", "quick", "vegetarian"],
  },
  {
    id: "2",
    title: "Grilled Chicken Salad with Avocado",
    image:
      "https://images.unsplash.com/photo-1512621776951-a57141f2eefd?ixlib=rb-1.2.1&auto=format&fit=crop&w=800&q=80",
    calories: 420,
    protein: 38,
    carbs: 15,
    fat: 22,
    prepTime: "20 min",
    healthScore: 8,
    url: "https://www.allrecipes.com/recipe/14126/grilled-chicken-salad/",
    tags: ["lunch", "high-protein", "low-carb", "keto-friendly"],
  },
  {
    id: "3",
    title: "Salmon with Roasted Vegetables",
    image:
      "https://images.unsplash.com/photo-1467003909585-2f8a72700288?ixlib=rb-1.2.1&auto=format&fit=crop&w=800&q=80",
    calories: 460,
    protein: 42,
    carbs: 18,
    fat: 24,
    prepTime: "30 min",
    healthScore: 10,
    url: "https://www.delish.com/cooking/recipe-ideas/a26950912/spicy-salmon-bowl-recipe/",
    tags: ["dinner", "high-protein", "omega-3", "low-carb"],
  },
  {
    id: "4",
    title: "Vegan Lentil Soup",
    image:
      "https://images.unsplash.com/photo-**********-85f173990554?ixlib=rb-1.2.1&auto=format&fit=crop&w=800&q=80",
    calories: 280,
    protein: 14,
    carbs: 48,
    fat: 4,
    prepTime: "40 min",
    healthScore: 9,
    url: "https://minimalistbaker.com/1-pot-everyday-lentil-soup/",
    tags: ["dinner", "vegan", "high-fiber", "low-fat"],
  },
  {
    id: "5",
    title: "Quinoa Bowl with Roasted Veggies",
    image:
      "https://images.unsplash.com/photo-1505253758473-96b7015fcd40?ixlib=rb-1.2.1&auto=format&fit=crop&w=800&q=80",
    calories: 380,
    protein: 12,
    carbs: 56,
    fat: 14,
    prepTime: "35 min",
    healthScore: 9,
    url: "https://cookieandkate.com/best-quinoa-salad-recipe/",
    tags: ["lunch", "vegetarian", "high-fiber", "meal-prep"],
  },
  {
    id: "6",
    title: "Protein Smoothie with Berries",
    image:
      "https://images.unsplash.com/photo-1502741224143-90386d7f8c82?ixlib=rb-1.2.1&auto=format&fit=crop&w=800&q=80",
    calories: 220,
    protein: 20,
    carbs: 28,
    fat: 3,
    prepTime: "5 min",
    healthScore: 8,
    url: "https://www.eatingwell.com/recipe/267192/really-green-smoothie/",
    tags: ["breakfast", "high-protein", "quick", "low-fat"],
  },
];

export default function ExploreScreen() {
  const { profile } = useUserProfile();
  const [category, setCategory] = useState<string>("all");
  const [isLoading, setIsLoading] = useState(false);

  // Animation values
  const fadeAnim = React.useRef(new Animated.Value(0)).current;
  const slideAnim = React.useRef(new Animated.Value(20)).current;

  useEffect(() => {
    animateContent(1);
  }, []);

  const animateContent = (toValue: number) => {
    Animated.parallel([
      Animated.spring(fadeAnim, {
        toValue,
        tension: 50,
        friction: 7,
        useNativeDriver: true,
      }),
      Animated.spring(slideAnim, {
        toValue: toValue === 1 ? 0 : 20,
        tension: 50,
        friction: 7,
        useNativeDriver: true,
      }),
    ]).start();
  };

  // Filter recipes based on user's goal and selected category
  const getFilteredRecipes = () => {
    let filtered = [...mockRecipes];

    // First filter by user's goal if available
    if (profile.initialized) {
      if (profile.goalType === "lose") {
        filtered = filtered.filter((recipe) => recipe.calories < 400);
      } else if (profile.goalType === "gain") {
        filtered = filtered.filter((recipe) => recipe.protein > 20);
      }
    }

    // Then filter by selected category
    if (category !== "all") {
      filtered = filtered.filter((recipe) => recipe.tags.includes(category));
    }

    return filtered;
  };

  const filteredRecipes = getFilteredRecipes();

  const handleOpenRecipe = (url: string) => {
    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
    Linking.openURL(url);
  };

  return (
    <View className="flex-1 bg-gray-950">
      <LinearGradient colors={["#111827", "#1F2937"]} className="flex-1">
        <ScrollView className="flex-1 p-5" scrollEventThrottle={16}>
          <Animated.View
            style={{
              opacity: fadeAnim,
              transform: [{ translateY: slideAnim }],
            }}
            className="items-start mb-6"
          >
            <Text className="text-4xl font-bold text-white mb-2">Explore</Text>
            <Text className="text-gray-400 text-lg">
              Discover healthy meals & recipes
            </Text>
          </Animated.View>

          {/* Category filters */}
          <Animated.View
            style={{
              opacity: fadeAnim,
              transform: [{ translateY: slideAnim }],
            }}
            className="mb-6"
          >
            <ScrollView
              horizontal
              showsHorizontalScrollIndicator={false}
              className="space-x-3"
            >
              {[
                "all",
                "high-protein",
                "low-carb",
                "vegetarian",
                "quick",
                "low-fat",
              ].map((cat) => (
                <TouchableOpacity
                  key={cat}
                  className={`py-2 px-4 rounded-full ${
                    category === cat ? "bg-green-500" : "bg-white/10"
                  }`}
                  onPress={() => {
                    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
                    setCategory(cat);
                  }}
                >
                  <Text
                    className={`${
                      category === cat ? "text-white" : "text-gray-300"
                    } font-semibold capitalize`}
                  >
                    {cat === "all" ? "All" : cat.replace("-", " ")}
                  </Text>
                </TouchableOpacity>
              ))}
            </ScrollView>
          </Animated.View>

          {/* Recipes section */}
          {isLoading ? (
            <View className="items-center py-12">
              <ActivityIndicator size="large" color="#4CAF50" />
              <Text className="text-gray-400 mt-2">Loading recipes...</Text>
            </View>
          ) : (
            <View className="space-y-6">
              {filteredRecipes.map((recipe, index) => (
                <Animated.View
                  key={recipe.id}
                  style={{
                    opacity: fadeAnim,
                    transform: [{ translateY: slideAnim }],
                  }}
                  className="bg-white/5 rounded-3xl overflow-hidden"
                >
                  <Image
                    source={{ uri: recipe.image }}
                    className="w-full h-48"
                    resizeMode="cover"
                  />
                  <BlurView intensity={30} className="p-5">
                    <Text className="text-xl font-bold text-white mb-2">
                      {recipe.title}
                    </Text>

                    <View className="flex-row flex-wrap mb-4">
                      {recipe.tags.map((tag) => (
                        <View
                          key={tag}
                          className="bg-white/10 rounded-full py-1 px-3 mr-2 mb-2"
                        >
                          <Text className="text-gray-300 text-xs capitalize">
                            {tag.replace("-", " ")}
                          </Text>
                        </View>
                      ))}
                    </View>

                    <View className="flex-row justify-between mb-4">
                      <View className="items-center">
                        <Text className="text-gray-400 text-xs">Calories</Text>
                        <Text className="text-white font-semibold">
                          {recipe.calories}
                        </Text>
                      </View>
                      <View className="items-center">
                        <Text className="text-gray-400 text-xs">Protein</Text>
                        <Text className="text-white font-semibold">
                          {recipe.protein}g
                        </Text>
                      </View>
                      <View className="items-center">
                        <Text className="text-gray-400 text-xs">Carbs</Text>
                        <Text className="text-white font-semibold">
                          {recipe.carbs}g
                        </Text>
                      </View>
                      <View className="items-center">
                        <Text className="text-gray-400 text-xs">Fat</Text>
                        <Text className="text-white font-semibold">
                          {recipe.fat}g
                        </Text>
                      </View>
                    </View>

                    <View className="flex-row items-center justify-between">
                      <View className="flex-row items-center">
                        <Ionicons name="time-outline" size={16} color="#888" />
                        <Text className="text-gray-400 ml-1">
                          {recipe.prepTime}
                        </Text>

                        <View className="flex-row items-center ml-4">
                          <Ionicons name="star" size={16} color="#FFD700" />
                          <Text className="text-gray-400 ml-1">
                            {recipe.healthScore}/10
                          </Text>
                        </View>
                      </View>

                      <TouchableOpacity
                        onPress={() => handleOpenRecipe(recipe.url)}
                        className="bg-gradient-to-r from-green-500 to-emerald-600 px-4 py-2 rounded-full"
                      >
                        <Text className="text-white font-semibold">
                          View Recipe
                        </Text>
                      </TouchableOpacity>
                    </View>
                  </BlurView>
                </Animated.View>
              ))}

              {filteredRecipes.length === 0 && (
                <Animated.View
                  style={{
                    opacity: fadeAnim,
                    transform: [{ translateY: slideAnim }],
                  }}
                  className="items-center justify-center py-12 bg-white/5 rounded-3xl"
                >
                  <Ionicons name="search-outline" size={64} color="#666" />
                  <Text className="text-gray-400 text-lg mt-4 text-center">
                    No recipes found for this category.
                  </Text>
                  <TouchableOpacity
                    onPress={() => setCategory("all")}
                    className="mt-4 bg-white/10 px-4 py-2 rounded-full"
                  >
                    <Text className="text-white">Show all recipes</Text>
                  </TouchableOpacity>
                </Animated.View>
              )}
            </View>
          )}
        </ScrollView>
      </LinearGradient>
    </View>
  );
}
