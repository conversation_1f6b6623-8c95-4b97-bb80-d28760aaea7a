import React, {
  createContext,
  useContext,
  useState,
  ReactNode,
  useEffect,
} from "react";
import { useColorScheme as useNativeColorScheme } from "react-native";
import { useColorScheme } from "nativewind";
import AsyncStorage from "@react-native-async-storage/async-storage";

type ThemeType = "light" | "dark";

interface ThemeContextType {
  theme: ThemeType;
  setTheme: (theme: ThemeType) => void;
  resolvedTheme: "light" | "dark";
  isLoading: boolean;
}

const THEME_STORAGE_KEY = "user-theme";

export const ThemeContext = createContext<ThemeContextType>({
  theme: "dark",
  setTheme: () => {},
  resolvedTheme: "dark",
  isLoading: true,
});

interface ThemeProviderProps {
  children: ReactNode;
}

export function ThemeProvider({ children }: ThemeProviderProps) {
  const deviceTheme = useNativeColorScheme();
  const { setColorScheme } = useColorScheme();
  // Initialize with device theme to avoid flash
  const [theme, setThemeState] = useState<ThemeType>(deviceTheme || "dark");
  const [resolvedTheme, setResolvedTheme] = useState<"light" | "dark">(
    deviceTheme || "dark"
  );
  const [isLoading, setIsLoading] = useState(true);

  // Load saved theme preference
  useEffect(() => {
    const loadSavedTheme = async () => {
      try {
        const savedTheme = await AsyncStorage.getItem(THEME_STORAGE_KEY);
        if (savedTheme) {
          setThemeState(savedTheme as ThemeType);
        } else {
          // If no saved theme, use device theme or dark if null
          setThemeState((deviceTheme || "dark") as ThemeType);
        }
      } catch (error) {
        console.error("Failed to load theme preference:", error);
        // On error, fallback to device theme
        setThemeState((deviceTheme || "dark") as ThemeType);
      } finally {
        // Always set loading to false when done
        setIsLoading(false);
      }
    };

    loadSavedTheme();
  }, [deviceTheme]);

  // Update the theme when it changes
  useEffect(() => {
    setResolvedTheme(theme);
    setColorScheme(theme);
  }, [theme, setColorScheme]);

  const setTheme = async (newTheme: ThemeType) => {
    try {
      await AsyncStorage.setItem(THEME_STORAGE_KEY, newTheme);
      setThemeState(newTheme);
    } catch (error) {
      console.error("Failed to save theme preference:", error);
    }
  };

  return (
    <ThemeContext.Provider
      value={{ theme, setTheme, resolvedTheme, isLoading }}
    >
      {children}
    </ThemeContext.Provider>
  );
}

export const useTheme = () => useContext(ThemeContext);
