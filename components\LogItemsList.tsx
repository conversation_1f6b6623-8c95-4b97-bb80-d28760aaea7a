import React from "react";
import { View, Text } from "react-native";
import Animated from "react-native-reanimated";
import { Ionicons } from "@expo/vector-icons";
import { useTheme } from "@/context/ThemeContext";
import { LogItem } from "./LogItem";
import { getShadowStyle } from "@/utils/shadowStyles";

interface LogItemsListProps {
  combinedLogItems: any[];
  isLoading: boolean;
  selectedImage: string | null;
  selectedDate: string;
  deleteFoodItem: (id: string, date: string) => Promise<void>;
  deleteExercise: (id: string, date: string) => Promise<void>;
  setForceUpdate: (value: React.SetStateAction<number>) => void;
  renderAnalyzingCard: () => React.ReactNode;
}

export function LogItemsList({
  combinedLogItems,
  isLoading,
  selectedImage,
  selectedDate,
  deleteFoodItem,
  deleteExercise,
  setForceUpdate,
  renderAnalyzingCard,
}: LogItemsListProps) {
  const { resolvedTheme } = useTheme();

  return (
    <View className={`${combinedLogItems.length > 0 ? "mt-4" : "mt-0"}`}>
      <View className="flex-row justify-between items-center mb-4">
        <Text
          className={`text-2xl font-bold ${
            resolvedTheme === "light" ? "text-gray-800" : "text-white"
          }`}
        >
          Today's Log
        </Text>
      </View>

      {/* Always show analyzing card at the top if it exists */}
      {renderAnalyzingCard()}

      {combinedLogItems.length === 0 && !isLoading && !selectedImage ? (
        <View
          className={`rounded-3xl p-6 items-center border backdrop-blur-xl ${
            resolvedTheme === "light"
              ? "bg-white/70 border-gray-200/50"
              : "bg-gray-800 border-gray-700"
          }`}
        >
          <View
            className={`w-16 h-16 rounded-full items-center justify-center mb-3 border ${
              resolvedTheme === "light"
                ? "bg-gray-100/60 border-gray-200/60"
                : "bg-gray-700 border-gray-600"
            }`}
          >
            <Ionicons
              name="journal-outline"
              size={30}
              color={resolvedTheme === "light" ? "#4b5563" : "#FFFFFF"}
            />
          </View>
          <Text
            className={`${
              resolvedTheme === "light" ? "text-gray-700" : "text-white"
            } font-semibold mb-1`}
          >
            No entries logged for this day
          </Text>
        </View>
      ) : (
        <View className="space-y-3">
          {combinedLogItems.map((item: any) => (
            <LogItem
              key={`${item.itemType}-${item.id}`}
              item={item}
              selectedDate={selectedDate}
              deleteFoodItem={deleteFoodItem}
              deleteExercise={deleteExercise}
              setForceUpdate={setForceUpdate}
            />
          ))}
        </View>
      )}
    </View>
  );
}
