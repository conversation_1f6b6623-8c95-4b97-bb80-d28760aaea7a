import React, { useState, useEffect } from "react";
import {
  View,
  Text,
  TextInput,
  ScrollView,
  Alert,
  TouchableOpacity,
  Platform,
  ActivityIndicator,
  Animated,
} from "react-native";
import { useRouter, useLocalSearchParams } from "expo-router";
import {
  useExercise,
  EXERCISE_TYPES,
  COMMON_EXERCISES,
  ExerciseType,
} from "@/context/ExerciseContext";
import { useUserProfile } from "@/context/UserProfileContext";
import { LinearGradient } from "expo-linear-gradient";
import { Ionicons } from "@expo/vector-icons";
import * as Haptics from "expo-haptics";
import { useTheme } from "@/context/ThemeContext";

export default function AddExerciseScreen() {
  const router = useRouter();
  const params = useLocalSearchParams();
  const { profile } = useUserProfile();
  const { calculateCaloriesBurned, addExercise } = useExercise();
  const { resolvedTheme } = useTheme();

  // Get date from params if provided, otherwise use today's date
  const selectedDate =
    params.date?.toString() || new Date().toISOString().split("T")[0];

  const [exerciseType, setExerciseType] = useState<ExerciseType>("cardio");
  const [exerciseName, setExerciseName] = useState<string>(
    COMMON_EXERCISES["cardio"][0]
  );
  const [duration, setDuration] = useState<string>("30");
  const [caloriesBurned, setCaloriesBurned] = useState<number>(0);
  const [isLoading, setIsLoading] = useState<boolean>(false);

  // Animation values
  const fadeAnim = React.useRef(new Animated.Value(0)).current;
  const slideAnim = React.useRef(new Animated.Value(20)).current;

  useEffect(() => {
    animateContent(1);
  }, []);

  const animateContent = (toValue: number) => {
    Animated.parallel([
      Animated.spring(fadeAnim, {
        toValue,
        tension: 50,
        friction: 7,
        useNativeDriver: true,
      }),
      Animated.spring(slideAnim, {
        toValue: toValue === 1 ? 0 : 20,
        tension: 50,
        friction: 7,
        useNativeDriver: true,
      }),
    ]).start();
  };

  useEffect(() => {
    if (
      profile?.weight &&
      exerciseName &&
      duration &&
      !isNaN(parseInt(duration)) &&
      parseInt(duration) > 0
    ) {
      const calories = calculateCaloriesBurned(
        profile.weight,
        exerciseName,
        parseInt(duration)
      );
      setCaloriesBurned(calories);
    } else {
      setCaloriesBurned(0);
    }
  }, [exerciseName, duration, profile?.weight, calculateCaloriesBurned]);

  const handleAddExercise = async () => {
    if (!exerciseName || !duration || parseInt(duration) <= 0) {
      Alert.alert(
        "Error",
        "Please select an exercise and enter a valid duration."
      );
      Haptics.notificationAsync(Haptics.NotificationFeedbackType.Error);
      return;
    }
    if (!profile?.weight) {
      Alert.alert("Error", "User weight is missing. Cannot add exercise.");
      Haptics.notificationAsync(Haptics.NotificationFeedbackType.Error);
      return;
    }

    setIsLoading(true);
    animateContent(0);
    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);

    try {
      // Short delay to ensure animation completes
      await new Promise((resolve) => setTimeout(resolve, 300));

      await addExercise({
        name: exerciseName,
        type: exerciseType,
        duration: parseInt(duration),
        caloriesBurned,
        date: selectedDate,
      });

      router.push(`/(tabs)?date=${selectedDate}`);
      Haptics.notificationAsync(Haptics.NotificationFeedbackType.Success);
    } catch (error) {
      console.error("Failed to add exercise:", error);
      Alert.alert("Error", "Failed to add exercise. Please try again.");
      Haptics.notificationAsync(Haptics.NotificationFeedbackType.Error);
    } finally {
      setIsLoading(false);
    }
  };

  // Helper function to render selection buttons (Type and Name)
  const renderSelectButton = (
    title: string,
    onPress: () => void,
    isActive: boolean
  ) => (
    <TouchableOpacity
      onPress={() => {
        onPress();
        Haptics.selectionAsync();
      }}
      className={`py-2 px-5 rounded-full m-1 border ${
        isActive ? "bg-white/20 border-white" : "bg-black/20 border-white/10"
      }`}
      disabled={isLoading}
    >
      <Text
        className={`font-medium text-sm capitalize ${
          isActive ? "text-white" : "text-gray-300"
        }`}
      >
        {title}
      </Text>
    </TouchableOpacity>
  );

  // StyledInput for modern input fields
  const StyledInput = ({
    label,
    placeholder,
    value,
    onChangeText,
    keyboardType = "default",
  }: any) => (
    <View>
      <Text
        className={
          resolvedTheme === "light"
            ? "text-gray-700 mb-1"
            : "text-gray-300 mb-1"
        }
      >
        {label}
      </Text>
      <View
        className={`$${
          resolvedTheme === "light"
            ? "bg-gray-100/60 border border-gray-200/60"
            : "bg-black/20 border border-gray-700"
        } rounded-xl p-1 h-14 mt-1`}
      >
        <TextInput
          className={`bg-transparent text-lg px-4 py-2 h-full ${
            resolvedTheme === "light" ? "text-gray-800" : "text-white"
          }`}
          placeholderTextColor={resolvedTheme === "light" ? "#888" : "#777"}
          value={value}
          onChangeText={onChangeText}
          placeholder={placeholder}
          keyboardType={keyboardType}
        />
      </View>
    </View>
  );

  return (
    <View
      className={`flex-1 ${
        resolvedTheme === "light" ? "bg-gray-50" : "bg-gray-950"
      }`}
    >
      <LinearGradient
        colors={
          resolvedTheme === "light"
            ? ["#f8fafc", "#e2e8f0", "#f1f5f9"]
            : ["#111827", "#1F2937"]
        }
        className="flex-1"
      >
        <View className="flex-1 px-4 pt-12">
          {/* Header */}
          <View className="flex-row items-center mb-8">
            <TouchableOpacity
              onPress={() => router.push(`/(tabs)?date=${selectedDate}`)}
              className={
                resolvedTheme === "light"
                  ? "bg-gray-100 p-3 rounded-full mr-4"
                  : "bg-white/10 p-3 rounded-full mr-4"
              }
            >
              <Ionicons
                name="arrow-back"
                size={22}
                color={resolvedTheme === "light" ? "#1f2937" : "white"}
              />
            </TouchableOpacity>
            <View className="flex-1">
              <Text
                className={`text-3xl font-bold ${
                  resolvedTheme === "light" ? "text-gray-800" : "text-white"
                }`}
              >
                Log Exercise
              </Text>
              <Text
                className={`${
                  resolvedTheme === "light" ? "text-gray-500" : "text-gray-400"
                } text-base mt-1`}
              >
                Track your workouts and calories burned
              </Text>
            </View>
          </View>
          {/* Content Area */}
          <ScrollView className="flex-1" showsVerticalScrollIndicator={false}>
            <Animated.View
              style={{
                opacity: fadeAnim,
                transform: [{ translateY: slideAnim }],
              }}
              className="w-full"
            >
              <View
                className={`rounded-3xl p-6 mb-6 backdrop-blur-xl ${
                  resolvedTheme === "light"
                    ? "bg-white border border-gray-200/60"
                    : "bg-gray-800 border border-gray-700"
                }`}
              >
                <View>
                  {/* Exercise Type Selection */}
                  <View className="mb-4">
                    <Text
                      className={
                        resolvedTheme === "light"
                          ? "text-gray-700 mb-1"
                          : "text-gray-300 mb-1"
                      }
                    >
                      Exercise Type
                    </Text>
                    <View
                      className={`flex-row rounded-xl p-2 mb-2 border ${
                        resolvedTheme === "light"
                          ? " border-gray-200/60"
                          : " border border-gray-700"
                      }`}
                    >
                      <ScrollView
                        horizontal
                        showsHorizontalScrollIndicator={false}
                        contentContainerStyle={{ paddingHorizontal: 2 }}
                      >
                        {EXERCISE_TYPES.map((type) => {
                          const displayName =
                            type === "cardio"
                              ? "Cardio"
                              : type === "strength"
                              ? "Strength"
                              : type === "flexibility"
                              ? "Flexibility"
                              : type === "sports"
                              ? "Sports"
                              : "Other";
                          const iconName =
                            type === "cardio"
                              ? "fitness"
                              : type === "strength"
                              ? "barbell"
                              : type === "flexibility"
                              ? "body"
                              : type === "sports"
                              ? "basketball"
                              : "options";
                          const isSelected = exerciseType === type;
                          return (
                            <TouchableOpacity
                              key={type}
                              onPress={() => {
                                Haptics.impactAsync(
                                  Haptics.ImpactFeedbackStyle.Light
                                );
                                setExerciseType(type);
                                setExerciseName(COMMON_EXERCISES[type][0]);
                              }}
                              className={`py-3 px-6 rounded-xl mx-1.5 border ${
                                isSelected
                                  ? resolvedTheme === "light"
                                    ? "bg-gray-100/60 border-gray-400"
                                    : "bg-black/20 border-gray-600"
                                  : resolvedTheme === "light"
                                  ? "bg-gray-100/60 border border-gray-200/60"
                                  : "bg-black/20 border border-gray-700"
                              }`}
                            >
                              <View className="flex-row items-center">
                                <Ionicons
                                  name={iconName}
                                  size={16}
                                  color={
                                    isSelected
                                      ? resolvedTheme === "light"
                                        ? "#1f2937"
                                        : "#fff"
                                      : resolvedTheme === "light"
                                      ? "#888"
                                      : "#888"
                                  }
                                  style={{ marginRight: 4 }}
                                />
                                <Text
                                  className={`font-medium text-base ${
                                    isSelected
                                      ? resolvedTheme === "light"
                                        ? "text-gray-800"
                                        : "text-white"
                                      : resolvedTheme === "light"
                                      ? "text-gray-500"
                                      : "text-gray-400"
                                  }`}
                                >
                                  {displayName}
                                </Text>
                              </View>
                            </TouchableOpacity>
                          );
                        })}
                      </ScrollView>
                    </View>
                  </View>
                  {/* Select Exercise Name */}
                  <View className="mb-4">
                    <Text
                      className={
                        resolvedTheme === "light"
                          ? "text-gray-700 mb-1"
                          : "text-gray-300 mb-1"
                      }
                    >
                      Select Exercise
                    </Text>
                    <View
                      className={`flex-row rounded-xl p-2 border ${
                        resolvedTheme === "light"
                          ? " border-gray-200/60"
                          : " border border-gray-700"
                      }`}
                    >
                      <ScrollView
                        horizontal
                        showsHorizontalScrollIndicator={false}
                        contentContainerStyle={{ paddingHorizontal: 4 }}
                      >
                        {COMMON_EXERCISES[exerciseType].map((name) => {
                          const isSelected = exerciseName === name;
                          return (
                            <TouchableOpacity
                              key={name}
                              onPress={() => {
                                Haptics.impactAsync(
                                  Haptics.ImpactFeedbackStyle.Light
                                );
                                setExerciseName(name);
                              }}
                              className={`py-3 px-5 rounded-lg mr-2 border ${
                                isSelected
                                  ? resolvedTheme === "light"
                                    ? "bg-gray-100/60 border-gray-400"
                                    : "bg-black/20 border-gray-600"
                                  : resolvedTheme === "light"
                                  ? "bg-gray-100/60 border border-gray-200/60"
                                  : "bg-black/20 border border-gray-700"
                              }`}
                            >
                              <Text
                                className={`font-medium capitalize ${
                                  isSelected
                                    ? resolvedTheme === "light"
                                      ? "text-gray-800"
                                      : "text-white"
                                    : resolvedTheme === "light"
                                    ? "text-gray-500"
                                    : "text-gray-400"
                                }`}
                              >
                                {name.replace(/_/g, " ")}
                              </Text>
                            </TouchableOpacity>
                          );
                        })}
                      </ScrollView>
                    </View>
                  </View>
                  {/* Duration Input */}
                  <View className="mb-4">
                    <StyledInput
                      label="Duration (minutes)"
                      placeholder="e.g., 30"
                      value={duration}
                      onChangeText={(text: string) =>
                        setDuration(text.replace(/[^0-9]/g, ""))
                      }
                      keyboardType="numeric"
                    />
                  </View>
                  {/* Calories Burned Display */}
                  <View className="mb-2">
                    <Text
                      className={
                        resolvedTheme === "light"
                          ? "text-gray-700 mb-1"
                          : "text-gray-300 mb-1"
                      }
                    >
                      Calories Burned
                    </Text>
                    <View
                      className={`rounded-xl px-6 py-4 items-center border ${
                        resolvedTheme === "light"
                          ? "bg-gray-100/60 border-gray-200/60"
                          : "bg-black/20 border border-gray-700"
                      }`}
                    >
                      <View className="flex-row items-center">
                        <Ionicons
                          name="flame"
                          size={28}
                          color="#FF5722"
                          style={{ marginRight: 10 }}
                        />
                        <Text
                          className={`text-3xl font-bold ${
                            resolvedTheme === "light"
                              ? "text-gray-800"
                              : "text-white"
                          }`}
                        >
                          {caloriesBurned.toFixed(0)}
                        </Text>
                      </View>
                      <Text
                        className={
                          resolvedTheme === "light"
                            ? "text-gray-500 mt-1"
                            : "text-gray-400 mt-1"
                        }
                      >
                        estimated calories
                      </Text>
                    </View>
                  </View>
                </View>
              </View>
            </Animated.View>
          </ScrollView>
          {/* Separator line */}
          <View
            className={`h-px w-full mb-6 ${
              resolvedTheme === "light" ? "bg-gray-200/60" : "bg-white/10"
            }`}
          />
          {/* Bottom Button */}
          <View className="pb-8">
            <TouchableOpacity
              className="py-4 rounded-2xl"
              onPress={handleAddExercise}
            >
              {isLoading ? (
                <ActivityIndicator
                  color={resolvedTheme === "light" ? "#1f2937" : "white"}
                  size="small"
                />
              ) : (
                <View className="flex-row items-center justify-center">
                  <Text
                    className={`text-center font-semibold text-lg ${
                      resolvedTheme === "light" ? "text-gray-800" : "text-white"
                    }`}
                  >
                    Add to Exercise Log
                  </Text>
                </View>
              )}
            </TouchableOpacity>
          </View>
        </View>
      </LinearGradient>
    </View>
  );
}
